# case分析系统项目说明文档

## 1. 项目概述

本项目是一个case分析系统。系统采用前后端分离架构。

## 2. 技术架构

### 2.1 后端架构 (csc-eval)
- 开发语言：Java
- 框架：Spring Boot
- 项目结构：根目录csc-eval
    - csc-eval-service：核心服务实现
      重要模块说明：
    1. com.meituan.csc.aigc.eval.dao, dao层，其中目录entity存放实体类（Po结尾的类）、mapper存放Maper结尾的接口类、service.generator存放自动生成的service接口类
    2. com.meituan.csc.aigc.eval.service，service层
    3. com.meituan.csc.aigc.eval.controller，controller层
    4. com.meituan.csc.aigc.eval.enums，存放枚举类
    5. resources/dao，存放XXMapper.xml文件


### 2.2 前端架构 (case-analysis-fe)
- 代码位置：
- 开发语言：Vue 3
- UI框架：Ant Design Vue
- 项目结构：
    - case-loop：Vue项目源码
      - src：源码目录
        - api: 接口目录
        - routes：路由目录
        - utils：工具目录
        - types：类型目录
        - views：页面目录
          - analysis：会话分析功能目录
            - 主要模块：
              - SessionAnalysis.vue：会话分析主页面，负责整体分析流程、数据展示与交互，是会话分析的核心页面。
              - SessionDetail.vue：会话详情页，展示单个会话的详细信息，包括会话内容、分析结果等。
              - SessionFavorite.vue：会话收藏页，管理和展示用户收藏的会话。
            - components：会话分析子模块组件目录
              - session-chat.vue、ivr-session-chat.vue：会话内容与消息展示组件，支持多种会话类型。
              - execution-plan-card.vue、aida-signal-card.vue、aida-exec-path-modal.vue、aida-exec-path-node-detail.vue：执行路径与信号相关组件，支持会话执行流程、信号流转等分析。
              - original-input-output-card.vue、model-info-panel.vue、llm-progress.vue、llm-progress-item.vue、llm-node-select.vue、error-set-llm-node-select.vue：输入输出与模型信息相关组件，支持模型进度、节点选择、错误集等功能。
              - service-process.vue、service-process/、aida-node-type-select.vue：服务流程与流程节点相关组件，支持服务流程可视化与节点类型选择。
              - inspect-diff-modal.vue、inspect-status-select.vue、call-detail-modal.vue：检查与分析相关组件，支持差异比对、状态筛选、调用详情等功能。
              - save-filter-button.vue、save-filter-modal.vue、save-search-modal.vue、favorite-filters-dropdown.vue、FavoriteFiltersDropdown.vue、FilterConditionModal.vue、SaveFilterModal.vue、SaveFilterButton.vue：过滤与收藏相关组件，支持条件过滤、收藏管理、筛选保存等功能。
              - workspace-app-select.vue、WorkspaceAppSelect.vue、AppVersionSelect.vue、SceneSelect.vue：应用、版本、场景等选择下拉组件。
              - audio-play.vue、add-error-set-modal.vue：音频播放、错误集管理等辅助功能组件。
              - 其他子目录（如online-message/、ivr-message/、service-process/等）：进一步细分的功能模块，支撑会话分析的多样化场景。



### 4.1 前端特点
- 采用AntD Design Vue组件库，提供统一的UI风格
- 响应式设计，支持多端适配
- 模块化开发，组件复用
- 使用Vue Router进行路由管理
- 支持环境配置（开发、测试、生产）

### 4.2 后端特点
- 微服务架构设计
- RESTful API接口
- 数据库事务管理
- 缓存机制
- 安全认证

## 5. 部署要求

### 5.1 环境要求
- Node.js >= 14.0.0
- Java >= 1.8
- MySQL >= 5.7
- Redis >= 6.0

### 5.2 配置要求
- 前端环境变量配置（.env.development/.env.production/.env.test）
- 后端配置文件（application.yml）
- 数据库配置
- Redis配置

## 6. 开发规范

### 6.1 代码规范
- 遵循ESLint规范
- 使用Prettier进行代码格式化
- 遵循Vue.js官方风格指南
- 遵循Java开发规范

### 6.2 组件开发规范
- 单一职责原则
- 组件文件不超过400行
- 复杂功能需要拆分组件
- 使用AntD组件库的标准组件

### 6.3 命名规范
- 组件名：PascalCase
- 文件名：kebab-case
- 变量名：camelCase
- 常量名：UPPER_CASE

## 7. 项目维护

### 7.1 版本控制
- 使用Git进行版本控制
- 遵循Git Flow工作流
- 规范的提交信息格式

### 7.2 文档维护
- 及时更新README文档
- 保持API文档的同步
- 记录重要的更新日志

## 8. 联系方式

如有问题请联系项目负责人或提交Issue。