import {createApp} from 'vue'
import App from './App.vue'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.less'
import * as Icons from '@ant-design/icons-vue'
import router from './router'
import './plugin/axios' // 引入axios插件
import {install as VueMonacoEditorPlugin} from '@guolao/vue-monaco-editor'


const app = createApp(App)

// 全局注册图标组件
const icons: any = Icons;
for (const i in icons) {
    app.component(i, icons[i])
}

app.use(Antd)
app.use(router)
app.use(VueMonacoEditorPlugin, {
    paths: {
        // 可以配置 Monaco Editor 的路径，这里使用默认的 CDN
        vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs'
    },
})

app.mount('#app')