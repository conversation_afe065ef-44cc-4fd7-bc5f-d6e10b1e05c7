<template>
  <div class="service-progress-container">
    <div class="service-card">
      <template>
        <!-- 头部信息区 -->
        <div class="service-header">
          <div class="scene-name" v-if="serviceData.sceneName">{{ serviceData.sceneName }}</div>
        </div>

        <!-- 订单信息 -->
        <div class="order-info" v-if="serviceData.orderDTO">
          <img
            v-if="serviceData.orderDTO?.orderImageUrl"
            :src="serviceData.orderDTO.orderImageUrl"
            class="order-image"
            alt="订单图片"
            @error="handleImageError"
            loading="lazy"
          />
          <div v-else class="order-image-placeholder">
            <mtd-icon name="mtdicon-pic" />
          </div>

          <div class="order-details">
            <div class="order-title" v-if="serviceData.orderDTO?.orderTitle" :title="serviceData.orderDTO.orderTitle">
              {{ serviceData.orderDTO.orderTitle }}
            </div>
            <div class="order-content" :title="formatOrderContent(serviceData.orderDTO?.orderContentList)">
              {{ formatOrderContent(serviceData.orderDTO?.orderContentList) }}
            </div>
            <div class="order-id" v-if="serviceData.orderDTO?.orderId">单号: {{ serviceData.orderDTO.orderId }}</div>
          </div>

          <div class="order-status-info">
            <div class="order-status" v-if="serviceData.orderDTO?.orderStatus">
              {{ serviceData.orderDTO.orderStatus }}
            </div>
            <div class="order-price" v-if="serviceData.orderDTO?.orderPrice">{{ serviceData.orderDTO.orderPrice }}</div>
          </div>
        </div>

        <!-- 进度信息 -->
        <div class="progress-info" v-if="serviceData.latestProgressNode">
          <div class="progress-node">
            <div class="node-circle"></div>
            <div class="node-content">
              <div class="node-name">{{ serviceData.latestProgressNode.nodeName }}</div>
              <div class="node-desc">{{ serviceData.latestProgressNode.nodeDesc }}</div>

              <div class="node-time">{{ serviceData.latestProgressNode.createTimeDisplay }}</div>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="service-footer" v-if="hasButtons">
          <a-button
            v-for="(button, index) in serviceData.bottomButtons"
            :key="index"
            class="action-button"
            :loading="loadingButtonId === button.id"
          >
            {{ button.name }}
          </a-button>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';

const props = defineProps({
  data: {
    type: String,
    required: true,
  },
});

const emit = defineEmits<{
  (e: 'button-click', button: ButtonInfo): void;
  (e: 'detail-click', url: string): void;
}>();

// 服务进度数据类型定义
interface OrderDTO {
  orderTitle?: string;
  orderTime?: number;
  orderPrice?: string;
  orderImageUrl?: string;
  orderStatus?: string;
  orderId?: string;
  orderContentList?: string[];
  extendMap?: Record<string, any>;
  showOrderList?: boolean;
  supportConsultBar?: boolean;
  isShow?: boolean;
}

interface ProgressNode {
  nodeName: string;
  nodeDesc: string;
  createTime: number;
  createTimeDisplay: string;
}

interface ButtonInfo {
  name: string;
  actionType: string;
  url: string;
  id: number;
  buttonType?: string;

  [key: string]: any;
}

interface ServiceProgressData {
  serviceType?: string;
  orderDTO?: OrderDTO;
  sceneName?: string;
  bottomButtons?: ButtonInfo[];
  latestProgressNode?: ProgressNode;
  detailUrl?: string;
  serviceId?: number;
  status?: number;

  [key: string]: any;
}

const serviceData = ref<ServiceProgressData>({});
const loadingButtonId = ref<number | null>(null);

// 判断是否有按钮要显示
const hasButtons = computed(() => {
  return serviceData.value.bottomButtons && serviceData.value.bottomButtons.length > 0;
});

// 处理图片加载错误
// eslint-disable-next-line @typescript-eslint/no-empty-function
const handleImageError = () => {};

// 格式化订单内容
const formatOrderContent = (contentList?: string[]): string => {
  if (!contentList || contentList.length === 0) {
    return '';
  }
  return contentList.join('、');
};

// 解析字符串为JSON
const safeParseJSON = (jsonString: string | any): any => {
  if (typeof jsonString !== 'string') {
    return jsonString;
  }

  try {
    return JSON.parse(jsonString);
  } catch (e) {
    // JSON parsing failed
    console.error('Parse json error:', e);
    return null;
  }
};

// 解析数据
const parseServiceData = () => {
  try {
    // 从originMessage中提取
    const originMessage = safeParseJSON(props.data) || {};
    // 更新服务数据
    serviceData.value = safeParseJSON(originMessage.data) || serviceData;
  } catch (e) {
    serviceData.value = {};
  }
};

// 监听数据变化
watch(() => props.data, parseServiceData, { immediate: true });

// 组件挂载时解析数据
onMounted(() => {
  parseServiceData();
  // console.log('服务进度组件已挂载'); // 保留注释
});
</script>

<style lang="scss" scoped>
.service-progress-container {
  width: fit-content;
  max-width: 294px;
  min-width: 294px;
}

.service-card {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  }

  &.loading {
    min-height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .loading-skeleton {
    width: 100%;
    padding: 14px;

    .skeleton-header {
      height: 22px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      margin-bottom: 14px;
      width: 40%;
    }

    .skeleton-content {
      height: 50px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      margin-bottom: 10px;
    }
  }

  .service-header {
    padding: 10px 14px;
    border-bottom: 1px solid #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .scene-name {
      font-size: 15px;
      font-weight: 500;
      color: #333;
    }

    .service-id {
      font-size: 12px;
      color: #999;
    }
  }

  .order-info {
    display: flex;
    padding: 14px;
    border-bottom: 1px solid #f5f5f5;

    .order-image,
    .order-image-placeholder {
      width: 60px;
      height: 60px;
      border-radius: 6px;
      overflow: hidden;
      flex-shrink: 0;
      background-color: #f8f8f8;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .order-image {
      object-fit: cover;
    }

    .order-image-placeholder {
      color: #ccc;
      font-size: 22px;
    }

    .order-details {
      margin-left: 10px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      overflow: hidden;

      .order-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .order-content {
        font-size: 13px;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 5px;
      }

      .order-id {
        font-size: 12px;
        color: #999;
      }
    }

    .order-status-info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: center;
      min-width: 70px;

      .order-status {
        font-size: 12px;
        color: #f60;
        margin-bottom: 6px;
        font-weight: 500;
      }

      .order-price {
        font-size: 13px;
        font-weight: 500;
        color: #333;
      }
    }
  }

  .progress-info {
    padding: 16px;

    .progress-node {
      position: relative;
      display: flex;
      padding-left: 16px;

      .node-circle {
        position: absolute;
        left: 0;
        top: 0;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #ffcd29;
        margin-top: 6px;
      }

      &::before {
        content: '';
        position: absolute;
        left: 4px;
        top: 16px;
        width: 2px;
        height: calc(100% - 16px);
        background: linear-gradient(to bottom, #ffcd29 0%, rgba(255, 205, 41, 0.2) 100%);
      }

      .node-content {
        flex: 1;
        margin-left: 12px;
      }

      .node-name {
        font-size: 15px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
      }

      .node-desc {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 8px;
      }

      .node-estimate {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 8px;
      }

      .node-time {
        font-size: 13px;
        color: #999;
      }
    }
  }

  .service-footer {
    display: flex;
    justify-content: center;
    padding: 8px;
    gap: 12px;

    .action-button {
      font-size: 14px;
      padding: 8px 24px;
      border-radius: 100px;
      background-color: #fff;
      color: #333;
      transition: all 0.2s ease;
      border: 1px solid #ddd;
      font-weight: normal;
      height: auto;

      &:hover {
        border-color: #ccc;
        background-color: #f9f9f9;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      }

      &:active {
        background-color: #f5f5f5;
      }
    }
  }

  .service-detail-link {
    display: flex;
    justify-content: center;
    padding: 10px 0;

    .detail-button {
      color: #1890ff;
      font-size: 13px;
      display: flex;
      align-items: center;
      transition: all 0.2s ease;

      .mtd-icon {
        margin-left: 4px;
        font-size: 12px;
        transition: transform 0.2s ease;
      }

      &:hover {
        color: #40a9ff;

        .mtd-icon {
          transform: translateX(3px);
        }
      }
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
