<template>
  <div class="robot-option-container">
    <div class="option-title" v-if="optionData.content">
      <span class="title-text">{{ optionData.content }}</span>
    </div>
    <div class="option-list">
      <div v-for="(option, index) in visibleOptions" :key="index" class="option-item">
        <div class="option-text">{{ option }}</div>
      </div>

      <div class="more-options" v-if="showMoreBtn" @click.stop="toggleShowAll">
        {{ showAll ? '收起选项' : '更多选项' }}
        <component :is="showAll ? 'UpOutlined' : 'DownOutlined'" class="more-icon" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { message } from 'ant-design-vue';

const props = defineProps<{
  data: string;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'option-selected', option: string, index: number): void;
}>();

// 选项数据接口
interface OptionData {
  content?: string;
  optionList?: string[];
  optionsDisplayStyle?: string;
  taskInstanceId?: string;
  taskType?: string;
  typicalQuestionId?: number;
  typicalQuestionName?: string;
  extendInfo?: Record<string, any>;
}

const optionData = ref<OptionData>({});
const showAll = ref(false);
const MAX_VISIBLE_OPTIONS = 5; // 默认显示的选项数量

// 是否显示更多按钮
const showMoreBtn = computed(() => {
  return (optionData.value.optionList?.length || 0) > MAX_VISIBLE_OPTIONS;
});

// 当前显示的选项列表
const visibleOptions = computed(() => {
  if (!optionData.value.optionList) return [];

  if (showAll.value) {
    return optionData.value.optionList;
  } else {
    return optionData.value.optionList.slice(0, MAX_VISIBLE_OPTIONS);
  }
});

// 切换显示全部/收起
const toggleShowAll = () => {
  showAll.value = !showAll.value;
};

// 解析数据
const parseOptionData = () => {
  try {
    // 解析原始消息
    const originMessage = typeof props.data === 'string' ? JSON.parse(props.data) : props.data;

    // 解析内部data字段
    if (originMessage.data) {
      try {
        const parsedData = typeof originMessage.data === 'string' ? JSON.parse(originMessage.data) : originMessage.data;

        // 使用默认值合并
        optionData.value = {
          content: '可选操作',
          optionList: [],
          ...parsedData,
        };
      } catch (e) {
        console.error('解析选项数据失败', e);
        optionData.value = { content: '可选操作', optionList: [] };
      }
    } else {
      // 使用默认值合并
      optionData.value = {
        content: '可选操作',
        optionList: [],
        ...originMessage,
      };
    }
  } catch (e) {
    console.error('解析原始数据失败', e);
    optionData.value = { content: '可选操作', optionList: [] };
  }
};

// 监听数据变化和组件加载
watch(() => props.data, parseOptionData, { immediate: true });

onMounted(() => {
  parseOptionData();
});
</script>

<style lang="scss" scoped>
.robot-option-container {
  width: fit-content;
  min-width: 294px;
  max-width: 294px;
  padding: 0;
  background: white;
  border-radius: 8px;

  .option-title {
    padding: 8px 12px;
    font-size: 13px;
    font-weight: 500;
    color: #212529;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0;
    word-break: break-word;
    overflow: hidden;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;

    .title-text {
      position: relative;
      padding-left: 10px;
      display: block;
      line-height: 1.5;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0.25em;
        height: calc(100% - 0.5em);
        min-height: 14px;
        width: 3px;
        background-color: #ffcb00;
        border-radius: 3px;
      }
    }
  }

  .option-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 10px;
    background-color: transparent;
    transition: all 0.3s ease;

    .option-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background-color: #f8f9fa;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid #e9ecef;
      margin-bottom: 8px;
      overflow: hidden;

      &:hover {
        background-color: #e9ecef;
        transform: translateY(-1px);
      }

      &:active {
        background-color: #e7f5ff;
      }

      .option-text {
        font-size: 12px;
        color: #495057;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        z-index: 1;
        position: relative;
      }

      .option-arrow {
        margin-left: 4px;
        font-size: 14px;
        color: #868e96;
        font-weight: bold;
        z-index: 1;
        position: relative;
      }
    }

    .more-options {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px 12px;
      color: #666;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      background-color: #f8f9fa;
      border-radius: 20px;
      border: 1px solid #e9ecef;

      .more-icon {
        margin-left: 5px;
        font-size: 11px;
        transition: transform 0.3s ease;
      }

      &:hover {
        color: #1c7ed6;
        background-color: #e9ecef;
        transform: translateY(-1px);
      }
    }
  }
}

// 动画效果
.option-fade-enter-active,
.option-fade-leave-active {
  transition: all 0.3s ease;
}

.option-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.option-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
