<template>
  <div>
    <!-- 语音输入来源 -->
    <template v-if="ivrOriginMessage.extraMap.inputType === IvrInputType.VOICE">
      <span>{{ ivrOriginMessage.extraMap.asrText || '--' }}</span>
    </template>
    <!-- 其他输入来源 -->
    <template v-else>
      <span>{{ ivrOriginMessage.extraMap.inputValue || '--' }}</span>
      <span v-if="ivrOriginMessage.extraMap.inputContent" class="msg-secondary">
        {{ `(${ivrOriginMessage.extraMap.inputContent})` }}
      </span>
    </template>
  </div>
</template>

<script setup lang="ts">
import { IvrInputType } from '@/constants/session-chat';

const props = defineProps<{
  ivrOriginMessage: IvrOriginMessageType;
}>();
</script>

<style lang="scss" scoped>
.msg-secondary {
  color: #999;
}
</style>
