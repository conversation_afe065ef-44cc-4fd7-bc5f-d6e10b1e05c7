<template>
  <a-select v-model:value="selectedNodeId" :allowClear="true" placeholder="选择大模型节点" @change="onNodeChange">
    <a-select-option v-for="{ nodeId, nodeName } in props.nodeList" :key="nodeId" :value="nodeId">
      <div class="llm-node-select-option-container">
        <img :src="RuleTaskNodeMap[TaskRuleBusinessType.LLM_CHAT].icon" style="height: 17px; width: 17px" />
        <div>
          {{ nodeName }}
        </div>
      </div>
    </a-select-option>
  </a-select>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { TaskRuleBusinessType, RuleTaskNodeMap } from '@/constants/aida-rule-task';

const props = withDefaults(
  defineProps<{
    nodeId?: string;
    nodeList?: Array<{ nodeId: string; nodeName: string } & Record<string, unknown>>;
  }>(),
  {
    nodeList: () => [],
    nodeId: 'node1',
  }
);

const selectedNodeId = ref('');

const selectedNode = computed(() => {
  return props.nodeList?.find((item) => item.nodeId === selectedNodeId.value) || null;
});

watch(
  () => props.nodeId,
  () => {
    selectedNodeId.value = props.nodeId;
  },
  { immediate: true }
);

const emit = defineEmits<{
  (e: 'change', nodeId: string): void;
}>();

const onNodeChange = (value: string) => {
  emit('change', value);
};
</script>

<style lang="scss" scoped>
:deep(.llm-node-select-label-container),
:deep(.llm-node-select-option-container) {
  height: 100%;
  display: flex;
  gap: 3px;
  align-items: center;
}
</style>
