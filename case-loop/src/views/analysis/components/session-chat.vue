<template>
  <div class="chat-inner-wrapper">
    <template v-for="(item, index) in props.sessionChatList" :key="index">
      <template v-if="item.senderType === SessionChatSenderType.SYSTEM">
        <!-- 可操作的大模型消息 -->
        <template v-if="isLlmMessageCanOperate(item)">
          <div class="system-message" ref="chatRefList">
            <div class="message-content">
              <div class="message-time">
                {{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}
                <!-- 暂时隐藏，支持子agent后可展示 -->
                <!-- <mtd-tag v-if="item.isLlm" theme="orange" size="small" type="ghost">模型消息</mtd-tag> -->
              </div>
              <div class="message-body">
                <div @click="onLLMMessageSelect(item, $event)">
                  <system-text-message
                    :message="item.message"
                    :is-active="true"
                    :is-active-llm="activeMessage?.messageId === item.messageId"
                  >
                    <template #additional-content>
                      <div class="llm-info" v-if="props.platformType === PlatformType.AIDA">
                        <div class="llm-info-header">
                          AI搭应用：<span style="background-color: #d5dbe1; padding: 2px 6px; border-radius: 4px">{{
                            item.messageLevelAidaAppInfo?.applicationName
                          }}</span>
                          <mtd-tooltip placement="top">
                            <div
                              class="llm-info-header-tag"
                              v-show="
                                item.basicInfo?.some((item) => item.displayName === '熔断类型') ||
                                item.basicInfo?.some((item) => item.displayName === '熔断标签')
                              "
                            >
                              <mtd-icon name="mtdicon-warning-circle-o" style="color: red" />
                              熔断信息
                            </div>
                            <template #content>
                              <div v-if="item.basicInfo?.some((item) => item.displayName === '熔断类型')">
                                熔断类型：{{
                                  item.basicInfo.find((item) => item.displayName === '熔断类型')?.displayContent
                                }}
                              </div>
                              <div v-if="item.basicInfo?.some((item) => item.displayName === '熔断标签')">
                                熔断标签：{{
                                  item.basicInfo.find((item) => item.displayName === '熔断标签')?.displayContent
                                }}
                              </div>
                            </template>
                          </mtd-tooltip>
                        </div>
                        <div
                          class="llm-info-content"
                          v-if="item.basicInfo?.some((item) => item.displayName === '执行动作')"
                        >
                          <div>执行动作：</div>
                          <div class="llm-info-content-item">
                            <mtd-icon name="mtdicon-point" style="color: #545454" />
                            {{
                              item.basicInfo?.find((item) => item.displayName === '执行动作')?.displayContent || '--'
                            }}
                          </div>
                        </div>
                      </div>
                    </template>
                  </system-text-message>
                </div>
                <div class="content-operate-container">
                  <div
                    class="inspect-button-wrapper"
                    :style="{
                      display: !item.manualInspect?.inspectResult ? 'none' : 'block',
                    }"
                  >
                    <mtd-icon-button
                      v-if="!isFromLabel"
                      :icon="
                        item.manualInspect?.inspectResult === SessionChatLLMInspectType.SUPPORT
                          ? 'fabulous-fill'
                          : 'fabulous'
                      "
                      style="color: #37b24d; background-color: #fff"
                      class="inspect-button-hover-style"
                      shape="square"
                      size="small"
                      @click.stop="onAgreeClick(item)"
                    />
                    <mtd-icon-button
                      v-if="!isFromLabel"
                      :icon="
                        item.manualInspect?.inspectResult === SessionChatLLMInspectType.UN_SUPPORT
                          ? 'fabulous-fill'
                          : 'fabulous'
                      "
                      style="transform: rotateX(180deg); color: #e03131; margin-left: 6px; background-color: #fff"
                      class="inspect-button-hover-style"
                      shape="square"
                      size="small"
                      @click.stop="onDisagreeClick(item)"
                    />
                    <mtd-button
                      size="small"
                      style="border: 0; color: #1c7ed6; margin-left: 6px; padding: 0 5px !important"
                      class="inspect-button-hover-style"
                      @click.stop="onMessageCollect(item)"
                    >
                      加入评测集
                    </mtd-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- 不可操作大模型消息 -->
        <template v-else>
          <!--   特殊处理 ChatBegin ui      -->
          <div
            class="system-message chat-begin-container"
            v-if="item.messageType === OnlineMessageTypeCode.CHAT_BEGIN"
            ref="chatRefList"
          >
            <div class="message-content">
              <div class="message-time">
                {{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}
              </div>
              <div class="message-body">
                <template>
                  <chat-begin :data="getMessageData(item)" />
                </template>
              </div>
            </div>
          </div>

          <!--   特殊处理 StaffCallOut ui      -->
          <div
            class="system-message staff-call-out-container"
            v-else-if="item.messageType === OnlineMessageTypeCode.STAFF_CALL_OUT"
            ref="chatRefList"
          >
            <div class="message-content">
              <div class="message-time">
                {{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}
              </div>
              <div class="message-body">
                <template>
                  <staff-call-out :data="getMessageData(item)" />
                </template>
              </div>
            </div>
          </div>

          <!--   普通系统消息      -->
          <div v-else class="system-message" ref="chatRefList">
            <div class="message-content">
              <div class="message-time">
                {{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}
              </div>
              <div class="message-body">
                <!-- 使用动态组件渲染不同类型消息 -->
                <Component
                  v-if="SystemMessageComponentMap[item.messageType]"
                  :is="SystemMessageComponentMap[item.messageType]"
                  :data="getMessageData(item)"
                />
                <template v-else>
                  <system-text-message :message="item.message" />
                </template>
              </div>
            </div>
          </div>
        </template>
      </template>
      <template v-if="item.senderType === SessionChatSenderType.CUSTOMER">
        <div class="customer-message" ref="chatRefList">
          <div class="message-content">
            <div class="message-time">
              <span class="name">用户</span>
              <span class="desc">{{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}</span>
            </div>
            <div class="message-body">
              <Component
                v-if="CustomerMessageComponentMap[item.messageType]"
                :is="CustomerMessageComponentMap[item.messageType]"
                :data="getMessageData(item)"
              />
              <template v-else>
                <customer-text-message :message="item.message" />
              </template>
            </div>
          </div>
        </div>
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
import {
  AidaApplicationType,
  OnlineMessageTypeCode,
  SessionChatClickType,
  SessionChatLLMInspectType,
  SessionChatSenderType,
} from '@/constants/session-chat';
import dayjs from 'dayjs';
import { ref, watch } from 'vue';
import OrderList from './online-message/order-list.vue';
import OrderDetail from './online-message/order-detail.vue';
import NoOrderList from './online-message/no-order-list.vue';
import CallOut from './online-message/call-out.vue';
import RobotText from './online-message/robot-text.vue';
import ChatQueueEnd from './online-message/chat-queue-end.vue';
import SessionImage from './online-message/session-image.vue';
import OrderCard from './online-message/order-card.vue';
import RobotOption from './online-message/robot-option.vue';
import ServiceProgress from './online-message/service-progress.vue';
import FaqCategoryList from './online-message/faq-category-list.vue';
import Relation from './online-message/relation-list.vue';
import { isFromLabelPlatform } from '@/utils/platform';
import { PlatformType } from '@/constants/platform';
import ChatBegin from './online-message/chat-begin.vue';
import CustomerTextMessage from './online-message/customer-text-message.vue';
import SystemTextMessage from './online-message/system-text-message.vue';
import SystemMiddle from './online-message/system-middle.vue';
import UpdateSessionQuery from './online-message/update-session-query.vue';
import FaqList from './online-message/faq-list.vue';
import RobotRecommend from './online-message/robot-recommend.vue';
import RecallRobotAnswerList from './online-message/recall-robot-answer-list.vue';
import StaffCallOut from './online-message/staff-call-out.vue';
import {useRouter} from 'vue-router';
import {useRoute} from 'vue-router';

const isFromLabel = ref(isFromLabelPlatform());
const props = defineProps<{
  sessionChatList: Array<WorkbenchSessionChatInfo>;
  /**
   * 会话来源平台
   */
  platformType?: PlatformType;
}>();

// 系统消息类型与对应组件的映射
const SystemMessageComponentMap: Record<string, Vue.Component> = {
  [OnlineMessageTypeCode.ORDER_LIST]: OrderList,
  [OnlineMessageTypeCode.ORDER_DETAIL]: OrderDetail,
  [OnlineMessageTypeCode.NO_ORDER_LIST]: NoOrderList,
  [OnlineMessageTypeCode.CALL_OUT]: CallOut,
  [OnlineMessageTypeCode.ROBOT_TEXT]: RobotText,
  [OnlineMessageTypeCode.CHAT_QUEUE_END]: ChatQueueEnd,
  [OnlineMessageTypeCode.SESSION_IMAGE]: SessionImage,
  [OnlineMessageTypeCode.ORDER_CARD]: OrderCard,
  [OnlineMessageTypeCode.ROBOT_OPTION]: RobotOption,
  [OnlineMessageTypeCode.SERVICE_PROGRESS]: ServiceProgress,
  [OnlineMessageTypeCode.FAQ_CATEGORY_LIST]: FaqCategoryList,
  [OnlineMessageTypeCode.RELATION]: Relation,
  [OnlineMessageTypeCode.CHAT_BEGIN]: ChatBegin,
  [OnlineMessageTypeCode.SYSTEM_MIDDLE]: SystemMiddle,
  [OnlineMessageTypeCode.UPDATE_SESSION_QUERY]: UpdateSessionQuery,
  [OnlineMessageTypeCode.FAQ_LIST]: FaqList,
  [OnlineMessageTypeCode.ROBOT_RECOMMEND]: RobotRecommend,
  [OnlineMessageTypeCode.RECALL_ROBOT_ANSWER_LIST]: RecallRobotAnswerList,
  [OnlineMessageTypeCode.STAFF_CALL_OUT]: StaffCallOut,
};

// 客户消息类型与对应组件的映射
const CustomerMessageComponentMap: Record<string, Vue.Component> = {
  [OnlineMessageTypeCode.ORDER_LIST]: OrderList,
  [OnlineMessageTypeCode.ORDER_DETAIL]: OrderDetail,
  [OnlineMessageTypeCode.NO_ORDER_LIST]: NoOrderList,
  [OnlineMessageTypeCode.CHAT_QUEUE_END]: ChatQueueEnd,
  [OnlineMessageTypeCode.SESSION_IMAGE]: SessionImage,
  [OnlineMessageTypeCode.ORDER_CARD]: OrderCard,
  [OnlineMessageTypeCode.ROBOT_OPTION]: RobotOption,
  [OnlineMessageTypeCode.SERVICE_PROGRESS]: ServiceProgress,
  [OnlineMessageTypeCode.CALL_OUT]: CallOut,
  [OnlineMessageTypeCode.ROBOT_TEXT]: RobotText,
  // 客户消息一般不会有 ChatBegin 类型
  [OnlineMessageTypeCode.FAQ_CATEGORY_LIST]: FaqCategoryList,
  [OnlineMessageTypeCode.RELATION]: Relation,
  [OnlineMessageTypeCode.UPDATE_SESSION_QUERY]: UpdateSessionQuery,
};

// 获取消息数据，处理字符串转换
const getMessageData = (item: WorkbenchSessionChatInfo) => {
  // 需要特殊处理JSON字符串的消息类型
  if (item.messageType === 'ChatBegin') {
    return item.message;
  }
  return item.originMessage;
};

const emits = defineEmits<{
  (
    event: 'llm-message-select',
    message: WorkbenchSessionChatInfo,
    clickType: SessionChatClickType,
    payload?: { inspectType?: SessionChatLLMInspectType }
  ): void;
}>();

const activeMessage = ref<WorkbenchSessionChatInfo>();
const route = useRoute();
const messageId = route.query.messageId as string;

const chatRefList = ref<Array<HTMLDivElement>>();

const onLLMMessageSelect = (message: WorkbenchSessionChatInfo, event?: MouseEvent) => {
  // 阻止事件冒泡
  event?.stopPropagation();
  const { isLlm } = message;
  if (!isLlm) return;
  activeMessage.value = message;

  emits('llm-message-select', message, SessionChatClickType.CLICK_MESSAGE);
};

watch(chatRefList, (refList) => {
  if (!refList) return;

  // debugger;
  // 如果URL中有messageId参数，则优先查找匹配该ID的消息
  if (messageId) {
    const urlMessageIndex = props.sessionChatList.findIndex(
      (item) => item.messageId === messageId && isLlmMessageCanOperate(item)
    );

    // 如果找到了匹配URL中messageId的消息，则选中该消息
    if (urlMessageIndex !== -1) {
      // debugger;
      // 滚动到该消息位置
      refList[urlMessageIndex].scrollIntoView({
        behavior: 'smooth',
      });

      // 选中该消息
      onLLMMessageSelect(props.sessionChatList[urlMessageIndex]);
      return;
    }
  }

  // 若URL中没有messageId或者找不到匹配的消息，则按原逻辑选中第一个可操作的大模型消息
  const operableLlmIndex = props.sessionChatList.findIndex((item) => isLlmMessageCanOperate(item));

  if (operableLlmIndex === -1) return;

  // * 选中匹配的用户会话（如果有上一个用户会话）
  const preIsLLMIndex = Math.max(0, operableLlmIndex - 1);
  refList[preIsLLMIndex].scrollIntoView({
    behavior: 'smooth',
  });

  // * 默认选中第一个模型信息
  onLLMMessageSelect(props.sessionChatList[operableLlmIndex]);
});

const onAgreeClick = (message: WorkbenchSessionChatInfo) => {
  const { isLlm } = message;
  if (!isLlm) return;
  activeMessage.value = message;
  emits('llm-message-select', message, SessionChatClickType.INSPECT, {
    inspectType: SessionChatLLMInspectType.SUPPORT,
  });
};

const onDisagreeClick = (message: WorkbenchSessionChatInfo) => {
  const { isLlm } = message;
  if (!isLlm) return;
  activeMessage.value = message;
  emits('llm-message-select', message, SessionChatClickType.INSPECT, {
    inspectType: SessionChatLLMInspectType.UN_SUPPORT,
  });
};

const onMessageCollect = (message: WorkbenchSessionChatInfo) => {
  const { isLlm } = message;
  if (!isLlm) return;
  activeMessage.value = message;
  emits('llm-message-select', message, SessionChatClickType.ADD_ERROR_SET);
};

/**
 * 判断是否为可操作的大模型消息
 * * 前提条件：isLlm === true
 * * 可选条件：当前平台为PB平台（applicationType为空）
 * * 可选条件：当前消息来自ai搭平台工作流应用（applicationType === AidaApplicationType.RULE）
 */
const isLlmMessageCanOperate = (message: WorkbenchSessionChatInfo) => {
  const { isLlm } = message;
  if (!isLlm) return false;
  // PB平台对话（applicationType为空），直接展示操作按钮即可；若为ai搭平台对话，只有当前（大模型）消息来自工作流应用，才可以进行操作（注：大模型消息还可能来自 文本/对话 型应用）
  return (
    props.platformType === PlatformType.PB ||
    message.messageLevelAidaAppInfo?.applicationType === AidaApplicationType.RULE
  );
};
</script>

<style lang="scss" scoped>
.chat-inner-wrapper {
  display: flex;
  flex-flow: column nowrap;
  background-color: #f0f2f5;
  border-radius: 8px;
  height: 100%;
  padding: 16px;
  overflow-y: auto;

  /* 系统消息样式 */
  .system-message {
    display: flex;
    flex-flow: row nowrap;
    align-items: flex-start;
    margin-top: 30px;
    width: 100%;
    justify-content: flex-start;

    &:first-child {
      margin-top: 0;
    }

    &::before {
      flex-shrink: 0;
      display: block;
      margin-right: 7px;
      content: '';
      background-image: url('https://nimg.ws.126.net/?url=https://dingyue.ws.126.net/2019/07/31/50a6ecea696d4191a2b1069f9f880c62.png&thumbnail=650x2147483647&quality=80&type=jpg');
      background-size: 100% 100%;
      width: 35px;
      height: 35px;
      border-radius: 50%;
    }

    .message-content {
      min-width: 100px;
      max-width: 100%;
      width: auto;
      display: flex;
      flex-direction: column;
      gap: 4px;
      align-items: flex-start;

      .message-time {
        color: rgba(0, 0, 0, 0.5);
        font-size: 10px;
        display: inline-flex;
        align-items: flex-end;
        gap: 6px;
      }

      .message-body {
        min-width: 160px;
        position: relative;

        /* 操作按钮容器样式 */
        .content-operate-container {
          position: absolute;
          // 按钮容器距离底部-12px = -按钮高度的一半
          bottom: -12px;
          right: 0;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 3px;
          margin-right: 8px;

          .inspect-button-wrapper {
            .inspect-button-hover-style {
              &:hover {
                background-color: rgba(17, 25, 37, 0.1) !important;
              }
            }
          }
        }

        /* 系统消息中的LLM信息样式 */
        .llm-info {
          display: flex;
          flex-direction: column;
          gap: 4px;
          border-top: 1px solid #c0c0c0;
          padding-top: 4px;
          margin-top: 4px;
          font-size: 12px;
          color: #545454;

          .llm-info-header {
            display: flex;
            align-items: center;

            .llm-info-header-tag {
              display: flex;
              align-items: center;
              margin-left: 8px;
            }
          }

          .llm-info-content {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .llm-info-content-item {
              display: flex;
              align-items: center;
              font-weight: 500;
            }
          }
        }
      }
    }

    // 鼠标悬浮时展示点赞/点踩按钮
    &:hover {
      .inspect-button-wrapper {
        display: block !important;
      }
    }

    /* ChatBegin容器样式 */
    &.chat-begin-container {
      justify-content: center !important;
      margin: 24px 0 !important;

      &::before {
        display: none !important;
      }

      .message-content {
        align-items: center !important;
        justify-content: center !important;

        .message-time {
          text-align: center !important;
        }
      }
    }

    /* staff-call-out容器样式 */
    &.staff-call-out-container {
      justify-content: center !important;
      margin: 24px 0 !important;

      &::before {
        display: none !important;
      }

      .message-content {
        max-width: 80%;
        align-items: center !important;
        justify-content: center !important;

        .message-time {
          text-align: center !important;
        }
      }
    }
  }

  /* 客户消息样式 */
  .customer-message {
    display: flex;
    flex-flow: row nowrap;
    align-items: flex-start;
    margin-top: 30px;
    width: 100%;
    justify-content: flex-end;

    &:first-child {
      margin-top: 0;
    }

    &::after {
      flex-shrink: 0;
      display: block;
      margin-left: 7px;
      content: '';
      background-image: url('https://cdn.mediecogroup.com/02/02f8f830/02f8f83013994bdf98b104f9fd5b0c83.jpg');
      background-size: 100% 100%;
      width: 35px;
      height: 35px;
      border-radius: 50%;
    }

    .message-content {
      min-width: 100px;
      max-width: 100%;
      width: auto;
      align-items: flex-end;

      .message-time {
        color: rgba(0, 0, 0, 0.5);
        font-size: 10px;
      }

      .message-body {
        position: relative;
      }
    }
  }
}
</style>
