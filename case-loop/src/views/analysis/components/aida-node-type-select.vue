<template>
  <a-select
    v-model:value="selectedNodeTypes"
    :allowClear="true"
    mode="multiple"
    :maxTagCount="1"
    placeholder="节点类型筛选"
    @change="onNodeTypesChange"
  >
    <a-select-option v-for="node in RuleTaskNodeList" :key="node.value" :value="node.value">
      <div class="aida-node-type-select-option-container">
        <img :src="node.icon" style="height: 17px; width: 17px" />
        <div>
          {{ node.label }}
        </div>
      </div>
    </a-select-option>
  </a-select>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { RuleTaskNodeMap, TaskRuleBusinessType, RuleTaskNodeList } from '@/constants/aida-rule-task';

const props = defineProps<{
  nodeTypes?: Array<TaskRuleBusinessType> | null;
}>();

const selectedNodeTypes = ref<Array<TaskRuleBusinessType>>([]);

watch(
  () => props.nodeTypes,
  () => {
    if (!props.nodeTypes) return;
    selectedNodeTypes.value = props.nodeTypes;
  },
  { immediate: true }
);

const emit = defineEmits<{
  (e: 'change', nodeTypes: Array<TaskRuleBusinessType>): void;
}>();

const onNodeTypesChange = (value: Array<TaskRuleBusinessType>) => {
  emit('change', value);
};

const getNodeName = (nodeType: TaskRuleBusinessType) => {
  return RuleTaskNodeMap[nodeType].name;
};
</script>

<style lang="scss" scoped>
:deep(.aida-node-type-select-option-container),
:deep(.aida-node-type-select-label-container) {
  display: flex;
  gap: 3px;
  align-items: center;
}
</style>
