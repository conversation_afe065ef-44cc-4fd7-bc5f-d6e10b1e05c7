<template>
  <div class="audio-play-wrapper">
    <audio ref="audioRef" :src="url" controls controlsList="nodownload" oncontextmenu="return false;" />
    <div class="speed-rate-icon-wrapper">
      <a-popover trigger="hover" placement="top">
        <img class="speed-rate-icon" src="@/assets/image/speed-rate.svg" alt="speed-rate" />
        <template #content>
          <div class="speed-rate-popover">
            <div
              v-for="rateItem in rateList"
              :key="rateItem.value"
              class="speed-rate-item"
              :class="{ 'speed-rate-item-active': currentRate === rateItem.value }"
              @click="
                () => {
                  currentRate = Number(rateItem.value);
                  if (audioRef) {
                    audioRef.playbackRate = Number(rateItem.value);
                  }
                }
              "
            >
              {{ rateItem.label }}
            </div>
          </div>
        </template>
      </a-popover>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import axios from 'axios';

const audioRef = ref<HTMLAudioElement>();
const currentRate = ref(1);

const props = withDefaults(
  defineProps<{
    /**
     * 音频来源，根据ivr解析逻辑梳理而来：
     * 1. tts：tts合成音频，根据tts参数获取音频即可
     * 2. url: 提供音频URL播放
     */
    audioType: 'tts' | 'url';
    originMessage?: IvrOriginMessageType;
    audioUrl?: string;
  }>(),
  {
    audioType: 'tts',
  }
);

const pause = () => {
  audioRef.value?.pause();
};
defineExpose({ pause });

const rateList = [
  { value: 0.25, label: '0.25' },
  { value: 0.5, label: '0.5' },
  { value: 0.75, label: '0.75' },
  { value: 1, label: '正常' },
  { value: 1.25, label: '1.25' },
  { value: 1.5, label: '1.5' },
  { value: 1.75, label: '1.75' },
  { value: 2, label: '2' },
];

const getTtsMessageUrl = async (message: IvrOriginMessageType) => {
  if (!message.extraMap || !message.extraMap.ttsParam || !message.extraMap.ttsSynthesisUrl) return '';
  const { ttsParam, ttsToken, ttsSynthesisUrl } = message.extraMap;
  const axiosWithoutInterceptor = axios.create();
  const res = await axiosWithoutInterceptor.post(ttsSynthesisUrl.replace(/^(https?):/, location.protocol), ttsParam, {
    responseType: 'blob',
    withCredentials: false,
    headers: {
      Token: ttsToken || '',
      SessionId: Math.random().toString(36).slice(-7),
    },
  });
  if (res?.data?.type === 'application/json') {
    return '';
  }
  const url = URL.createObjectURL(res.data);
  return url;
};

const url = ref('');

const initAudioUrl = async () => {
  if (props.audioType === 'tts' && props.originMessage) {
    url.value = await getTtsMessageUrl(props.originMessage);
  } else if (props.audioType === 'url' && props.audioUrl) {
    url.value = props.audioUrl;
  }
};

initAudioUrl();
</script>

<style lang="scss" scoped>
.audio-play-wrapper {
  width: 100%;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #f1f3f4;
  border-radius: 8px;
  margin: 4px 0 6px 0;
  position: relative;
  .speed-rate-icon-wrapper {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 10px;
    top: 4;
    background-color: #f1f3f4;
    .speed-rate-icon {
      width: 30px;
      height: 30px;
      color: black;
    }
    &:hover {
      background-color: #e1e3e5;
      border-radius: 15px;
    }
  }
}

.speed-rate-popover {
  padding: 8px;
  .speed-rate-item {
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 4px;
    transition: background-color 0.2s;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover,
    &.speed-rate-item-active {
      background-color: #e1e3e5;
    }
  }
}
</style>
