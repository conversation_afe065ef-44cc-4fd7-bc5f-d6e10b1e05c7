<template>
  <a-card class="signal-card">
    <template #title>
      <div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
        <div style="display: flex; align-items: center; gap: 4px">
          信号信息
        </div>
        <!-- 展开收起按钮 -->
        <a-button
            type="text"
            size="small"
            @click="toggleExpanded"
            class="expand-toggle-btn"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <component
              :is="isExpanded ? 'UpOutlined' : 'DownOutlined'"
              style="margin-left: 2px; font-size: 12px"
          />
        </a-button>
      </div>
    </template>

    <!-- 信号信息卡片内容 -->
    <div class="record-info-card-container" v-if="hasSignalData">
      <!-- 执行后信号 -->
      <div v-if="displaySignalAfterData.length" class="signal-section">
        <div class="signal-list">
          <div class="signal-item" v-for="(item, index) in displaySignalAfterData" :key="`after-${index}`">
            <span class="signal-label">{{ item.signalName }}:</span>
            <span class="signal-value" :class="{ 'signal-changed': item.isChange }">{{
                item.signalValue || '--'
              }}</span>
            <div class="signal-actions">
              <!-- 变化标识 -->
              <ArrowUpOutlined
                  v-if="item.isChange"
                  style="color: #52c41a"
                  :title="'信号值已变化'"
              />
              <text-show-modal
                  v-if="item.signalValue"
                  class="text-show-modal-style"
                  :value="item.signalValue"
                  :title="item.signalName"
                  :read-only="true"
                  placeholder=""
              />
              <!-- 收藏按钮 -->
              <component
                  :is="item.isFavorite ? 'StarFilled' : 'StarOutlined'"
                  :style="{
                  color: item.isFavorite ? '#faad14' : '#8c8c8c',
                  cursor: 'pointer',
                  fontSize: '18px',
                  transition: 'all 0.2s ease'
                }"
                  :title="item.isFavorite ? '取消收藏' : '收藏'"
                  @click="toggleFavorite('after', index, item)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-text">暂无信号信息</div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import {computed, ref} from 'vue';
import TextShowModal from '@/components/TextShowModal.vue';
import {changeSignalFavoriteApi} from "@/api/workbench-session";

// 定义 props
const props = defineProps<{
  queryMessageLlmInfo?: any;
}>();


// 展开状态
const isExpanded = ref(false);

// 计算执行前信号数据
const signalBeforeData = computed(() => {
  if (!props.queryMessageLlmInfo?.allSignal?.signalBeforeList) return [];
  return props.queryMessageLlmInfo.allSignal.signalBeforeList;
});

// 计算执行后信号数据
const signalAfterData = computed(() => {
  if (!props.queryMessageLlmInfo?.allSignal?.signalAfterList) return [];
  return props.queryMessageLlmInfo.allSignal.signalAfterList;
});


// 计算要显示的执行后信号数据（根据展开状态）
const displaySignalAfterData = computed(() => {
  if (isExpanded.value) {
    return signalAfterData.value;
  }
  return signalAfterData.value.filter(item => item.isFavorite);
});

// 判断是否有信号数据
const hasSignalData = computed(() => {
  return signalBeforeData.value.length > 0 || signalAfterData.value.length > 0;
});

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

// 切换收藏状态
const toggleFavorite = async (type: 'before' | 'after', index: number, signal: any) => {
  const {workspaceId, applicationId, applicationVersion} = props.queryMessageLlmInfo.messageLevelAidaAppInfo || {};

  // 切换收藏状态
  signal.isFavorite = !signal.isFavorite;

  try {
    await changeSignalFavoriteApi({
      platformWorkspace: workspaceId,
      appId: applicationId,
      appVersionId: applicationVersion,
      isFavorite: signal.isFavorite,
      signalKey: signal.signalKey,
    });
  } catch (error) {
    console.error("收藏信号失败", error)
    // message.error(error);
  }

  // 这里可以添加API调用来保存收藏状态
  console.log(`${signal.isFavorite ? '收藏' : '取消收藏'}信号:`, signal.signalName);
};


</script>

<style scoped lang="scss">
.record-info-card-container {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .signal-section {
    .signal-section-title {
      font-size: 14px;
      font-weight: 600;
      color: #111925;
      margin-bottom: 8px;
      padding-left: 8px;
      border-left: 3px solid #fa8c16;
    }

    .signal-list {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .signal-item {
        display: flex;
        align-items: center;
        gap: 12px;
        min-height: 32px;
        padding: 4px 0;

        .signal-label {
          flex: 0 0 auto;
          font-size: 14px;
          font-weight: 500;
          color: #111925a6;
          min-width: 100px;
        }

        .signal-value {
          flex: 1;
          font-size: 14px;
          color: #000;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &.signal-changed {
            color: #52c41a;
            font-weight: 500;
          }
        }

        .signal-actions {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;

          .text-show-modal-style {
            color: #166ff7;
            font-weight: 700;
            display: none;
          }

          // 确保收藏按钮始终可见
          .anticon {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
          }
        }

        &:hover {
          .text-show-modal-style {
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}

.expand-toggle-btn {
  color: #1890ff !important;
  padding: 2px 8px !important;
  font-size: 12px !important;
  height: 24px !important;
  line-height: 20px !important;
  border-radius: 4px !important;

  &:hover {
    background-color: rgba(24, 144, 255, 0.1) !important;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;

  .empty-text {
    color: #999;
    font-size: 14px;
  }
}

// 收藏按钮样式
:deep(.favorite-star) {
  filter: drop-shadow(0 0 6px rgba(250, 173, 20, 1)) !important;
  background: radial-gradient(circle, rgba(250, 173, 20, 0.4) 0%, transparent 80%) !important;
  border-radius: 50% !important;
  padding: 4px !important;
  box-shadow: 0 0 10px rgba(250, 173, 20, 0.8) !important;
  position: relative !important;

  &:before {
    content: '' !important;
    position: absolute !important;
    top: -2px !important;
    left: -2px !important;
    right: -2px !important;
    bottom: -2px !important;
    background: radial-gradient(circle, rgba(250, 173, 20, 0.3) 0%, transparent 60%) !important;
    border-radius: 50% !important;
    z-index: -1 !important;
  }

  &:hover {
    transform: scale(1.15) !important;
    filter: drop-shadow(0 0 8px rgba(250, 173, 20, 1)) !important;
    box-shadow: 0 0 15px rgba(250, 173, 20, 1) !important;
  }
}

// 未收藏按钮hover效果
:deep(.anticon) {
  &:hover {
    color: #faad14 !important;
    transform: scale(1.05) !important;
    filter: drop-shadow(0 0 2px rgba(250, 173, 20, 0.5)) !important;
  }
}

// 确保所有收藏按钮都可见
:deep(.anticon) {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 10 !important;
}
</style>
