<template>
  <div class="progress-inner-wrapper">
    <!-- PB应用的执行链路 -->
    <!-- 注：未来PB逻辑会下掉，删除此部分即可 -->
    <template v-if="props.platformType === PlatformType.PB">
      <div class="pb-title-wrapper">
        <a-select
          v-model:value="selectedTraceTypeList"
          placeholder="请选择"
          mode="multiple"
          :allowClear="true"
          :filterOption="true"
          :maxTagCount="1"
          :options="traceTypeList"
        />
      </div>
      <div class="pb-progress" ref="pbProgressRef">
        <a-collapse v-model:activeKey="activeStep">
          <template v-for="(item, index) in filteredList" :key="index">
            <a-collapse-panel :header="getTitle(item.type)" :key="index">
              <template v-if="item.input">
                <llm-editor title="输入" v-model="item.input" :style="editorWrapperStyle" class="editor-item" />
              </template>
              <template v-if="item.output">
                <llm-editor title="输出" v-model="item.output" :style="editorWrapperStyle" class="editor-item" />
              </template>
              <template v-if="item.model">
                <llm-editor title="使用模型" v-model="item.model" :style="editorWrapperStyle" class="editor-item" />
              </template>
              <template v-if="item.process">
                <llm-editor title="过程数据" v-model="item.process" :style="editorWrapperStyle" class="editor-item" />
              </template>
            </a-collapse-panel>
          </template>
        </a-collapse>
      </div>
    </template>

    <!-- ai搭应用执行链路 -->
    <template v-if="props.platformType === PlatformType.AIDA">
      <div class="aida-title-wrapper">
        <aida-node-type-select :node-types="selectedAidaNodeTypes" @change="onAidaNodeChange" />
      </div>
      <div class="aida-progress">
        <template v-for="item in filteredAidaData" :key="`${item.applicationId}-${item.nodeId}`">
          <llm-progress-item :node="item" />
        </template>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import LlmEditor from '@/components/llm-editor.vue';
import { PlatformType } from '@/constants/platform';
import { computed, ref, toRefs, watch } from 'vue';
import { TaskRuleBusinessType } from '@/constants/aida-rule-task';
import AidaNodeTypeSelect from './aida-node-type-select.vue';
import LlmProgressItem from './llm-progress-item.vue';

const props = withDefaults(
  defineProps<{
    platformType?: PlatformType;
    /**
     * PB执行链路数据，platformType为PB时必传
     */
    data?: WorkbenchSessionChatLLMInfo['detail']['traceList'] | null;
    /**
     * ai搭执行链路数据，platformType为AIDA时必传
     */
    aidaData?: Array<TAidaTraceListItem> | null;
  }>(),
  {
    platformType: PlatformType.PB,
    data: () => [],
  }
);

const { data: list } = toRefs(props);

const activeStep = ref(-1);

const traceTypeList = ref<Array<{ label: string; value: string }>>([
  {
    label: '大模型',
    value: '1',
  },
  {
    label: '系统接口',
    value: '2',
  },
  {
    label: '规则引擎',
    value: '3',
  },
]);

// ! 默认选择全部
const selectedTraceTypeList = ref<Array<string>>(
  Array.from(
    new Set(
      list.value?.map((item) => {
        return item.type;
      }) || []
    )
  )
);

const filteredList = computed(
  () => list.value?.filter((item) => selectedTraceTypeList.value.includes(item.type)) || []
);

const pbProgressRef = ref<HTMLElement>();

const editorWrapperStyle = ref<{ width: string; height: string }>({
  width: '100%',
  height: '150px'  // 修改为固定 150px 高度，与 AI 搭一致
});

const getTitle = (type: string) => {
  return traceTypeList.value.find((item) => item.value === type)?.label || '--';
};

watch(filteredList, (val) => {
  if (activeStep.value < val.length) return;

  activeStep.value = -1;
});

const selectedAidaNodeTypes = ref<Array<TaskRuleBusinessType>>([]);

// 根据选中的节点类型对aidaData进行过滤
const filteredAidaData = computed(() => {
  if (!props.aidaData) return [];
  // 若未选择节点类型，则不进行过滤，展示全量数据即可
  if (!selectedAidaNodeTypes.value.length) return props.aidaData;
  return props.aidaData.filter((item) => selectedAidaNodeTypes.value.includes(item.nodeType));
});

const onAidaNodeChange = (val: Array<TaskRuleBusinessType>) => {
  selectedAidaNodeTypes.value = val;
};
</script>

<style lang="scss" scoped>
.progress-inner-wrapper {
  flex: 1;
  display: flex;
  flex-flow: column nowrap;

  .pb-title-wrapper,
  .aida-title-wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 8px;
  }

  .pb-progress {
    flex: 1;
    overflow-y: auto;

    :deep(.ant-collapse-item) {
      .editor-item {
        margin-top: 10px;  // 调整间距与 AI 搭一致

        &:first-child {
          margin-top: 0;
        }
      }
    }
  }

  .aida-progress {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
}
</style>
