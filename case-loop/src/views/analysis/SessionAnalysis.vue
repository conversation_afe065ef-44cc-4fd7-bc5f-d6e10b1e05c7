<template>
  <div class="session-analysis">
    <!-- Tab切换器 -->
    <a-tabs v-model:activeKey="activeTab" class="session-tabs">
      <!-- 会话查询Tab -->
      <a-tab-pane key="query" tab="会话查询">
        <!-- 筛选区域 -->
        <a-card class="filter-card" :bordered="false">
      <a-form layout="vertical" class="compact-form">
        <a-row :gutter="16">
          <!-- 会话ID -->
          <a-col :span="8">
            <a-form-item label="会话ID" class="compact-form-item">
              <a-input
                v-model:value="searchFormData.sessionId"
                placeholder="请输入会话ID，多个ID请用英文逗号分隔"
                allow-clear
              >
                <template #suffix>
                  <a-button
                    type="text"
                    size="small"
                    @click="openSessionIdModal"
                    :title="'批量输入会话ID'"
                    style="padding: 0; margin-right: 4px; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;"
                  >
                    <template #icon>
                      <ExpandOutlined style="font-size: 12px;" />
                    </template>
                  </a-button>
                </template>
              </a-input>
            </a-form-item>
          </a-col>

          <!-- 创建时间 -->
          <a-col :span="8">
            <a-form-item label="创建时间" class="compact-form-item">
              <a-range-picker
                  v-model:value="searchFormData.date"
                  style="width: 100%"
                  :format="dateFormat"
                  :disabledDate="disabledDate"
                  :placeholder="['开始时间', '结束时间']"
              />
            </a-form-item>
          </a-col>

          <!-- 空间/应用 -->
          <a-col :span="8">
            <a-form-item label="空间/应用" class="compact-form-item">
              <workspace-app-select
                  v-model="searchFormData.workspaceAndApp"
                  :form-data="searchFormData"
                  :scene-relation-config="sceneRelationConfig"
                  @change="handleWorkspaceAppChange"
              />
            </a-form-item>
          </a-col>

          <!-- 应用版本 -->
          <a-col :span="8">
            <a-form-item label="应用版本" class="compact-form-item">
              <app-version-select
                  v-model="searchFormData.applicationVersionId"
                  :form-data="searchFormData"
                  :scene-relation-config="sceneRelationConfig"
                  :is-applying-saved-filter="isApplyingSavedFilter"
                  @change="handleAppVersionChange"
              />
            </a-form-item>
          </a-col>

          <!-- 场景 -->
          <a-col :span="8" v-if="isFilterAdded('scene')">
            <a-form-item label="场景" class="compact-form-item">
              <scene-select
                  v-model="searchFormData.scene"
                  :form-data="searchFormData"
                  :scene-relation-config="sceneRelationConfig"
                  @change="handleSceneChange"
              />
            </a-form-item>
          </a-col>

          <!-- VisitID -->
          <a-col :span="8" v-if="isFilterAdded('visitId')">
            <a-form-item label="VisitID" class="compact-form-item">
              <a-input v-model:value="searchFormData.visitId" placeholder="请输入VisitID" allow-clear/>
            </a-form-item>
          </a-col>

          <!-- 用户输入检索 -->
          <a-col :span="8" v-if="isFilterAdded('userInput')">
            <a-form-item label="用户输入检索" class="compact-form-item">
              <a-input v-model:value="searchFormData.userInput" placeholder="请输入用户输入内容" allow-clear/>
            </a-form-item>
          </a-col>

          <!-- 回复内容检索 -->
          <a-col :span="8" v-if="isFilterAdded('finalOutput')">
            <a-form-item label="回复内容检索" class="compact-form-item">
              <a-input v-model:value="searchFormData.finalOutput" placeholder="请输入回复内容" allow-clear/>
            </a-form-item>
          </a-col>

          <!-- 更多筛选项 (根据配置动态添加) -->
          <template v-for="filter in visibleFilters" :key="filter.key">
            <a-col :span="8"
                   v-if="isFilterAdded(filter.key) && !['sessionId', 'date', 'workspaceAndApp', 'applicationVersionId', 'scene', 'visitId', 'userInput', 'finalOutput'].includes(filter.key)">
              <a-form-item :label="filter.label" class="compact-form-item">
                <!-- 选择框类型 -->
                <a-select
                    v-if="filter.type === 'select'"
                    v-model:value="searchFormData[filter.key]"
                    :placeholder="`请选择${filter.label}`"
                    allow-clear
                    :mode="filter.multiple ? 'multiple' : undefined"
                >
                  <a-select-option
                      v-for="option in getFilterOptions(filter.key)"
                      :key="option.value"
                      :value="option.value"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>

                <!-- 输入框类型 -->
                <a-input
                    v-else-if="filter.type === 'input'"
                    v-model:value="searchFormData[filter.key]"
                    :placeholder="`请输入${filter.label}`"
                    allow-clear
                />

                <!-- 特殊组件类型 -->
                <component
                    v-else-if="filter.type === 'component'"
                    :is="filter.component"
                    v-model="searchFormData[filter.key]"
                    :form-data="searchFormData"
                    :scene-relation-config="sceneRelationConfig"
                    :is-applying-saved-filter="isApplyingSavedFilter"
                    @change="componentChangeHandlers[filter.key] || (() => {})"
                />

                <!-- 业务选择组件 -->
                <business-select
                    v-else-if="filter.key === 'bu'"
                    v-model="searchFormData.bu"
                    :form-data="searchFormData"
                    :scene-relation-config="sceneRelationConfig"
                    @change="handleBusinessChange"
                />

                <!-- 子业务选择组件 -->
                <sub-business-select
                    v-else-if="filter.key === 'subBu'"
                    v-model="searchFormData.subBu"
                    :form-data="searchFormData"
                    :scene-relation-config="sceneRelationConfig"
                    :is-applying-saved-filter="isApplyingSavedFilter"
                    @change="handleSubBusinessChange"
                />

                <!-- 触发task选择组件 -->
                <task-key-select
                    v-else-if="filter.key === 'taskKey'"
                    v-model="searchFormData.taskKey"
                    :form-data="searchFormData"
                    :scene-relation-config="sceneRelationConfig"
                    :is-applying-saved-filter="isApplyingSavedFilter"
                    @change="handleTaskKeyChange"
                />

                <!-- 触发task版本选择组件 -->
                <task-version-select
                    v-else-if="filter.key === 'taskVersion'"
                    v-model="searchFormData.taskVersion"
                    :form-data="searchFormData"
                    :is-applying-saved-filter="isApplyingSavedFilter"
                    @change="handleTaskVersionChange"
                />

                <!-- 触发task节点选择组件 -->
                <task-node-select
                    v-else-if="filter.key === 'taskNode'"
                    v-model="searchFormData.taskNode"
                    :form-data="searchFormData"
                    :is-applying-saved-filter="isApplyingSavedFilter"
                    @change="handleTaskNodeChange"
                />
              </a-form-item>
            </a-col>
          </template>

          <!-- 更多自定义筛选项按钮 -->
          <a-col :span="8">
            <a-form-item class="compact-form-item" style="margin-bottom: 12px;">
              <a-button @click="handleAddFilterClick" class="custom-button" style="width: 100%;">
                <template #icon>
                  <PlusOutlined/>
                </template>
                更多自定义筛选项
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 操作按钮和常用筛选 -->
        <a-row>
          <a-col :span="12">
            <a-space>
              <favorite-filters-dropdown
                  :favorites="favoriteFilters"
                  @select="applyFavoriteFilter"
                  @remove="handleDeleteFavoriteFilter"
                  @manage="openManageFavoritesModal"
              />
            </a-space>
          </a-col>
          <a-col :span="12" style="text-align: right">
            <a-space>
              <a-button @click="handleClearClick" class="custom-button">
                <template #icon>
                  <ReloadOutlined/>
                </template>
                重置
              </a-button>
              <a-button type="primary" @click="handleSearch" class="custom-button">
                <template #icon>
                  <SearchOutlined/>
                </template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>

        <!-- 已选筛选项标签区域 -->
        <div class="selected-filters-tags" v-if="hasActiveFilters">
          <div class="selected-filters-label">已选筛选项：</div>
          <div class="selected-filters-list">
            <!-- 会话ID筛选项 -->
            <a-tag
              v-if="searchFormData.sessionId"
              color="blue"
              closable
              class="selected-filter-tag"
              @close="clearFilter('sessionId')"
              style="margin: 2px 4px 2px 0;"
            >
              会话ID: {{ getDisplayValue('sessionId', searchFormData.sessionId) }}
            </a-tag>

            <!-- 创建时间筛选项 -->
            <a-tag
              v-if="searchFormData.date && searchFormData.date.length === 2"
              color="blue"
              closable
              class="selected-filter-tag"
              @close="clearFilter('date')"
              style="margin: 2px 4px 2px 0;"
            >
              创建时间: {{ getDisplayValue('date', searchFormData.date) }}
            </a-tag>

            <!-- 空间/应用筛选项 -->
            <a-tag
              v-if="searchFormData.workspaceAndApp"
              color="blue"
              closable
              class="selected-filter-tag"
              @close="clearFilter('workspaceAndApp')"
              style="margin: 2px 4px 2px 0;"
            >
              空间/应用: {{ getDisplayValue('workspaceAndApp', searchFormData.workspaceAndApp) }}
            </a-tag>

            <!-- 应用版本筛选项 -->
            <a-tag
              v-if="searchFormData.applicationVersionId"
              color="blue"
              closable
              class="selected-filter-tag"
              @close="clearFilter('applicationVersionId')"
              style="margin: 2px 4px 2px 0;"
            >
              应用版本: {{ getDisplayValue('applicationVersionId', searchFormData.applicationVersionId) }}
            </a-tag>

            <!-- 场景筛选项 -->
            <a-tag
              v-if="searchFormData.scene"
              color="blue"
              closable
              class="selected-filter-tag"
              @close="clearFilter('scene')"
              style="margin: 2px 4px 2px 0;"
            >
              场景: {{ getDisplayValue('scene', searchFormData.scene) }}
            </a-tag>

            <!-- VisitID筛选项 -->
            <a-tag
              v-if="searchFormData.visitId"
              color="blue"
              closable
              class="selected-filter-tag"
              @close="clearFilter('visitId')"
              style="margin: 2px 4px 2px 0;"
            >
              VisitID: {{ getDisplayValue('visitId', searchFormData.visitId) }}
            </a-tag>

            <!-- 用户输入检索筛选项 -->
            <a-tag
              v-if="searchFormData.userInput"
              color="blue"
              closable
              class="selected-filter-tag"
              @close="clearFilter('userInput')"
              style="margin: 2px 4px 2px 0;"
            >
              用户输入检索: {{ getDisplayValue('userInput', searchFormData.userInput) }}
            </a-tag>

            <!-- 回复内容检索筛选项 -->
            <a-tag
              v-if="searchFormData.finalOutput"
              color="blue"
              closable
              class="selected-filter-tag"
              @close="clearFilter('finalOutput')"
              style="margin: 2px 4px 2px 0;"
            >
              回复内容检索: {{ getDisplayValue('finalOutput', searchFormData.finalOutput) }}
            </a-tag>

            <!-- 动态筛选项 -->
            <template v-for="filter in visibleFilters" :key="filter.key">
              <a-tag
                v-if="isFilterAdded(filter.key) && hasFilterValue(filter.key) && !['sessionId', 'date', 'workspaceAndApp', 'applicationVersionId', 'scene', 'visitId', 'userInput', 'finalOutput'].includes(filter.key)"
                color="blue"
                closable
                class="selected-filter-tag"
                @close="clearFilter(filter.key)"
                style="margin: 2px 4px 2px 0;"
              >
                {{ filter.label }}: {{ getDisplayValue(filter.key, searchFormData[filter.key]) }}
              </a-tag>
            </template>

            <!-- 保存按钮 -->
            <a-button
              type="primary"
              size="small"
              @click="openSaveFilterModal"
              class="save-filter-btn"
              style="margin-left: 8px;"
            >
              <template #icon>
                <SaveOutlined />
              </template>
              保存
            </a-button>
          </div>
        </div>
      </a-form>
    </a-card>

    <!-- 筛选条件添加模态框 -->
    <filter-condition-modal
        v-model:visible="filterConditionModalVisible"
        :available-filters="availableFilters"
        :selected-filters="selectedFilters"
        @confirm="handleFilterConfirm"
    />

    <!-- 会话ID批量输入模态框 -->
    <a-modal
      v-model:visible="sessionIdModalVisible"
      title="批量输入会话ID"
      width="600px"
      :destroyOnClose="true"
      @ok="handleSessionIdModalOk"
      @cancel="handleSessionIdModalCancel"
    >
      <div style="margin-bottom: 16px;">
        <a-alert
          type="info"
          show-icon
          style="margin-bottom: 16px"
        >
          <template #message>
            请输入会话ID，每行一个或用英文逗号分隔。支持批量粘贴。
          </template>
        </a-alert>

        <a-textarea
          v-model:value="sessionIdTempValue"
          placeholder="请输入会话ID，支持以下格式：&#10;1. 每行一个ID&#10;2. 用英文逗号分隔&#10;3. 混合格式&#10;&#10;例如：&#10;session123&#10;session456, session789&#10;session101"
          :auto-size="{ minRows: 8, maxRows: 15 }"
          style="resize: vertical;"
        />
      </div>

      <div style="color: #666; font-size: 12px;">
        <div>提示：</div>
        <div>• 支持每行一个ID或用逗号分隔</div>
        <div>• 自动去除空行和重复ID</div>
        <div>• 当前已输入：{{ getSessionIdCount() }} 个ID</div>
      </div>
    </a-modal>

    <!-- 数据表格 -->
    <a-card style="margin-top: 16px" :bordered="false">
      <template #title>
        <div class="table-header">
          <div class="table-title">会话列表</div>
          <div class="table-actions">
            <!-- 表格操作按钮可以在这里添加，如导出等 -->
          </div>
        </div>
      </template>

      <a-table
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :row-key="record => record.sessionId"
          @change="handleTableChange"
      >
        <!-- 会话ID列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'sessionId'">
            <a @click="viewSessionDetail(record)">{{ record.sessionId }}</a>
          </template>

          <!-- 空间/应用列 -->
          <template v-if="column.key === 'workspaceApp'">
            <div v-if="record.workspaceAppInfo && record.workspaceAppInfo.length > 0">
              <a-tag color="blue" v-for="ws in record.workspaceAppInfo" :key="ws.workspaceId">
                {{ ws.workspaceName }}
              </a-tag>
              <a-tag color="green" v-for="app in record.workspaceAppInfo.flatMap(ws => ws.applicationList)"
                     :key="app.applicationId">
                {{ app.applicationName }}
              </a-tag>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 场景列 -->
          <template v-if="column.dataIndex === 'sceneName'">
            <a-tag v-if="record.sceneName" :color="getScenarioColor(record.sceneName)">
              {{ record.sceneName }}
            </a-tag>
            <span v-else>-</span>
          </template>

          <!-- 转人工列 -->
          <template v-if="column.dataIndex === 'transferStaff'">
            <template v-if="record.transferStaff !== null && record.transferStaff !== undefined">
              <a-tag :color="getTransferStaffTagColor(record.transferStaff)">
                {{ getTransferStaffTagText(record.transferStaff) }}
              </a-tag>
            </template>
            <span v-else>-</span>
          </template>

          <!-- 解决情况列 -->
          <template v-if="column.dataIndex === 'sessionSolved'">
            <template v-if="record.sessionSolved !== null && record.sessionSolved !== undefined">
              <a-tag :color="getSolvedTagColor(record.sessionSolved)">
                {{ getSolvedTagText(record.sessionSolved) }}
              </a-tag>
            </template>
            <span v-else>-</span>
          </template>

          <!-- 满意度列 -->
          <template v-if="column.dataIndex === 'stars'">
            <div v-if="record.stars !== null && record.stars !== undefined">
              <a-rate :value="record.stars" disabled :count="5"/>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a @click="viewSessionDetail(record)">查看</a>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

        <!-- 保存筛选条件模态框 -->
        <save-filter-modal
            v-model:visible="saveFilterModalVisible"
            :filter-data="currentFilter"
            @save="handleSaveFilter"
        />
      </a-tab-pane>

      <!-- 会话收藏Tab -->
      <a-tab-pane key="favorite" tab="会话收藏">
        <SessionFavorite :active="activeTab === 'favorite'" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import {defineComponent, ref, reactive, computed, onMounted, watch} from 'vue';
import {message} from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  ExportOutlined,
  DeleteOutlined,
  ExpandOutlined,
  CloseOutlined,
  SaveOutlined
} from '@ant-design/icons-vue';
import {useRouter} from 'vue-router';
import dayjs from 'dayjs';

// 导入自定义组件
import WorkspaceAppSelect from './components/online-search/online-workspace-app-select.vue';
import AppVersionSelect from './components/online-search/online-app-version-select.vue';
import SceneSelect from './components/online-search/online-scene-select.vue';
import BusinessSelect from './components/online-search/online-business-select.vue';
import SubBusinessSelect from './components/online-search/online-sub-business-select.vue';
import TaskKeySelect from './components/online-search/online-task-key-select.vue';
import TaskVersionSelect from './components/online-search/online-task-version-select.vue';
import TaskNodeSelect from './components/online-search/online-task-node-select.vue';
import FilterConditionModal from './components/FilterConditionModal.vue';
import FavoriteFiltersDropdown from './components/FavoriteFiltersDropdown.vue';
import SaveFilterButton from './components/SaveFilterButton.vue';
import SaveFilterModal from './components/SaveFilterModal.vue';
import SessionFavorite from './SessionFavorite.vue';

// 导入API
import {
  getSessionConditionConfig,
  getSessionConditionScene,
  getSessionPage,
  getSessionConditionTaskVersions,
  getSessionConditionTaskNodes,
  deleteSavedFilter,
  getSavedFilterList,
  saveFilterCondition
} from '@/api/analysis';

const LOCAL_STORAGE_KEY = 'sessionAnalysisFilters';

// Placeholder for the analysis type string needed by favorite filter APIs
const FAVORITE_FILTER_ANALYSIS_TYPE = 'online'; // Adjust if a different value is known

export default defineComponent({
  name: 'SessionAnalysis',
  components: {
    WorkspaceAppSelect,
    AppVersionSelect,
    SceneSelect,
    BusinessSelect,
    SubBusinessSelect,
    TaskKeySelect,
    TaskVersionSelect,
    TaskNodeSelect,
    FilterConditionModal,
    FavoriteFiltersDropdown,
    SaveFilterButton,
    SaveFilterModal,
    SessionFavorite,
    SearchOutlined,
    ReloadOutlined,
    PlusOutlined,
    ExportOutlined,
    DeleteOutlined,
    ExpandOutlined,
    CloseOutlined,
    SaveOutlined
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const tableData = ref([]);

    // Tab相关状态
    const activeTab = ref('query');

    // 筛选表单数据
    const searchFormData = reactive({
      sessionId: '',
      date: [
        dayjs().subtract(7, 'day').startOf('day'),
        dayjs().endOf('day')
      ],
      workspaceAndApp: null,
      applicationVersionId: null,
      scene: null,
      bu: null,
      subBu: null,
      taskKey: null,
      taskVersion: null,
      taskNode: null,
      visitId: '',
      userInput: '',
      finalOutput: ''
    });

    // 场景关系配置
    const sceneRelationConfig = ref({
      sceneInfos: []
    });

    // 筛选条件模态框
    const filterConditionModalVisible = ref(false);
    const availableFilters = ref([]);
    const selectedFilters = ref([]);
    const isApplyingSavedFilter = ref(false);

    // 本地缓存key
    const SELECTED_FILTERS_KEY = 'sessionAnalysisSelectedFilters';

    // 会话ID输入模态框
    const sessionIdModalVisible = ref(false);
    const sessionIdTempValue = ref('');

    // 表格列配置
    const columns = [
      {
        title: '会话ID',
        dataIndex: 'sessionId',
        key: 'sessionId',
        width: 200,
        fixed: 'left'
      },
      {
        title: '创建时间',
        dataIndex: 'time',
        key: 'time',
        width: 170,
        sorter: true,
        customRender: ({text}) => {
          return text ? new Date(text).toLocaleString() : '-';
        }
      },
      {
        title: '空间/应用',
        key: 'workspaceApp',
        width: 150,
        customRender: ({record}) => {
          if (record.workspaceAppInfo && record.workspaceAppInfo.length > 0) {
            const workspaceNames = record.workspaceAppInfo.map(ws => ws.workspaceName);
            const appNames = record.workspaceAppInfo.flatMap(ws =>
                ws.applicationList.map(app => app.applicationName)
            );
            return `${workspaceNames.join(', ')}/${appNames.join(', ')}`;
          }
          return '-';
        }
      },
      {
        title: '场景',
        dataIndex: 'sceneName',
        key: 'sceneName',
        width: 150
      },

      {
        title: '转人工',
        dataIndex: 'transferStaff',
        key: 'transferStaff',
        width: 100
      },
      {
        title: '是否解决',
        dataIndex: 'sessionSolved',
        key: 'sessionSolved',
        width: 100
      },
      {
        title: '满意度',
        dataIndex: 'stars',
        key: 'stars',
        width: 150
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 100,
        fixed: 'right'
      }
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true
    });

    // 表格选择
    const selectedRowKeys = ref([]);
    const hasSelectedRows = computed(() => selectedRowKeys.value.length > 0);

    // 收藏的筛选条件
    const favoriteFilters = ref([]);
    const currentFilter = reactive({
      name: '',
      conditions: {}
    });

    // 保存筛选条件模态框
    const saveFilterModalVisible = ref(false);

    // 可见的筛选项
    const visibleFilters = computed(() => {
      return selectedFilters.value.filter(filter => !filter.hidden);
    });

    // 新增：判断是否有已选筛选项
    const hasActiveFilters = computed(() => {
      return selectedFilters.value.some(filter => {
        const val = searchFormData[filter.key];
        if (Array.isArray(val)) return val.length > 0;
        return val !== undefined && val !== null && val !== '';
      });
    });

    // 新增：task相关options的loading与缓存
    const taskVersionOptions = ref([]);
    const taskNodeOptions = ref([]);
    const taskVersionLoading = ref(false);
    const taskNodeLoading = ref(false);

    // 动态获取taskVersion options
    const fetchTaskVersionOptions = async () => {
      taskVersionLoading.value = true;
      taskVersionOptions.value = [];
      if (!searchFormData.taskKey) {
        taskVersionLoading.value = false;
        return;
      }
      try {
        const params = {
          ...buildQueryParams(),
          taskKey: searchFormData.taskKey
        };
        const res = await getSessionConditionTaskVersions(params);
        if (res.code === 0 && Array.isArray(res.data)) {
          taskVersionOptions.value = res.data.map(item => ({
            value: item.value,
            label: item.label
          }));
        }
      } finally {
        taskVersionLoading.value = false;
      }
    };

    // 动态获取taskNode options
    const fetchTaskNodeOptions = async () => {
      taskNodeLoading.value = true;
      taskNodeOptions.value = [];
      if (!searchFormData.taskKey || !searchFormData.taskVersion) {
        taskNodeLoading.value = false;
        return;
      }
      try {
        const params = {
          ...buildQueryParams(),
          taskKey: searchFormData.taskKey,
          taskVersion: searchFormData.taskVersion
        };
        const res = await getSessionConditionTaskNodes(params);
        if (res.code === 0 && Array.isArray(res.data)) {
          taskNodeOptions.value = res.data.map(item => ({
            value: item.value,
            label: item.label
          }));
        }
      } finally {
        taskNodeLoading.value = false;
      }
    };

    // 获取筛选项options
    const getFilterOptions = (key) => {
      console.log('[getFilterOptions] key:', key);
      // 场景
      if (key === 'scene') {
        const opts = sceneRelationConfig.value.sceneOptions.map(opt => ({
          value: opt.value,
          label: opt.label
        }));
        console.log('[getFilterOptions] sceneOptions:', opts);
        return opts;
      }
      // 空间/应用
      if (key === 'workspaceAndApp') {
        const sceneId = searchFormData.scene;
        const scene = sceneRelationConfig.value.workspaceAppOptions.find(s => s.sceneId === sceneId);
        console.log('[getFilterOptions] workspaceAndApp sceneId:', sceneId, 'scene:', scene);
        if (!scene) return [];
        const opts = (scene.workspaceAppList || []).map(ws => ({
          value: ws.workspaceId,
          label: ws.workspaceName,
          applicationList: ws.applicationList
        }));
        console.log('[getFilterOptions] workspaceAppOptions:', opts);
        return opts;
      }
      // 应用版本
      if (key === 'applicationVersionId') {
        const sceneId = searchFormData.scene;
        const workspaceId = searchFormData.workspaceAndApp && searchFormData.workspaceAndApp[0];
        const applicationId = searchFormData.workspaceAndApp && searchFormData.workspaceAndApp[1];
        const scene = sceneRelationConfig.value.workspaceAppOptions.find(s => s.sceneId === sceneId);
        console.log('[getFilterOptions] applicationVersionId sceneId:', sceneId, 'workspaceId:', workspaceId, 'applicationId:', applicationId, 'scene:', scene);
        if (!scene) return [];
        const ws = (scene.workspaceAppList || []).find(w => w.workspaceId === workspaceId);
        if (!ws) return [];
        const app = (ws.applicationList || []).find(a => a.applicationId === applicationId);
        if (!app) return [];
        const opts = (app.robotList || []).map(robot => ({
          value: robot.robotId,
          label: robot.robotName
        }));
        console.log('[getFilterOptions] applicationVersionOptions:', opts);
        return opts;
      }
      // 业务、子业务、触发task现在由online-search组件内部处理，这里返回空数组
      if (key === 'bu' || key === 'subBu' || key === 'taskKey') {
        console.log(`[getFilterOptions] ${key} 由online-search组件内部处理`);
        return [];
      }
      // 转人工筛选项
      if (key === 'transferStaff') {
        const opts = [
          { value: 'noChatRequest', label: '未转人工' },
          { value: 'noChatBegin', label: '在线转人工（成功接入）' },
          { value: 'chatBegin', label: '在线转人工（未接入）' },
          { value: 'transferBD', label: '转BD' }
        ];
        console.log('[getFilterOptions] transferStaffOptions:', opts);
        return opts;
      }
      // 满意度筛选项
      if (key === 'stars') {
        const opts = [
          { value: '1', label: '1星' },
          { value: '2', label: '2星' },
          { value: '3', label: '3星' },
          { value: '4', label: '4星' },
          { value: '5', label: '5星' }
        ];
        console.log('[getFilterOptions] starsOptions:', opts);
        return opts;
      }
      // 其余保持原有逻辑
      const filter = (availableFilters.value || []).find(item => item.key === key);
      const opts = filter?.options || [];
      console.log('[getFilterOptions] default options:', opts);
      return opts;
    };

    // 处理场景变更
    const handleSceneChange = (value) => {
      searchFormData.scene = value;
      searchFormData.workspaceAndApp = null;
      searchFormData.bu = null;
      searchFormData.subBu = null;
      searchFormData.taskKey = null;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };
    // 处理业务变更
    const handleBusinessChange = (value) => {
      searchFormData.bu = value;
      searchFormData.subBu = null;
      searchFormData.taskKey = null;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };
    // 处理子业务变更
    const handleSubBusinessChange = (value) => {
      searchFormData.subBu = value;
      searchFormData.taskKey = null;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };

    // 处理taskKey变更
    const handleTaskKeyChange = (value) => {
      searchFormData.taskKey = value;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };
    // 处理taskVersion变更
    const handleTaskVersionChange = (value) => {
      searchFormData.taskVersion = value;
      searchFormData.taskNode = null;
    };
    // 处理taskNode变更
    const handleTaskNodeChange = (value) => {
      searchFormData.taskNode = value;
    };

    // 处理工作空间应用变更
    const handleWorkspaceAppChange = (value) => {
      searchFormData.workspaceAndApp = value;
      searchFormData.applicationVersionId = null;
      searchFormData.scene = null;
      searchFormData.bu = null;
      searchFormData.subBu = null;
      searchFormData.taskKey = null;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };
    // 处理应用版本变更
    const handleAppVersionChange = (value) => {
      searchFormData.applicationVersionId = value;
      searchFormData.scene = null;
      searchFormData.bu = null;
      searchFormData.subBu = null;
      searchFormData.taskKey = null;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };

    // 组件变更处理器
    const componentChangeHandlers = {
      taskKey: handleTaskKeyChange,
      taskVersion: handleTaskVersionChange,
      taskNode: handleTaskNodeChange,
      workspaceAndApp: handleWorkspaceAppChange,
      applicationVersionId: handleAppVersionChange,
      scene: handleSceneChange,
      bu: handleBusinessChange,
      subBu: handleSubBusinessChange
    };

    // 初始化
    onMounted(async () => {
      await fetchFilterConfig();
      await fetchSceneRelationConfig();
      await loadFavoriteFilters();
      fetchData();
    });

    // 初始化时优先从localStorage恢复selectedFilters
    const loadSelectedFiltersFromLocalStorage = () => {
      const raw = localStorage.getItem(SELECTED_FILTERS_KEY);
      console.log('[loadSelectedFiltersFromLocalStorage] localStorage数据:', raw);
      if (raw) {
        try {
          const keys = JSON.parse(raw);
          console.log('[loadSelectedFiltersFromLocalStorage] 解析的keys:', keys);
          // 只保留当前API返回的可用筛选项
          const validKeys = (availableFilters.value || []).map(f => f.key);
          selectedFilters.value = (keys || []).map(k => (availableFilters.value || []).find(f => f.key === k)).filter(Boolean);
          console.log('[loadSelectedFiltersFromLocalStorage] 设置的selectedFilters:', selectedFilters.value.map(f => f.key));
        } catch (e) {
          console.log('[loadSelectedFiltersFromLocalStorage] 解析失败，重置为空数组');
          selectedFilters.value = [];
        }
      } else {
        console.log('[loadSelectedFiltersFromLocalStorage] 没有localStorage数据');
      }
    };

    // fetchFilterConfig时，availableFilters存全部，selectedFilters优先读localStorage
    const fetchFilterConfig = async () => {
      try {
        const res = await getSessionConditionConfig();
        console.log('[fetchFilterConfig] API返回:', res);
        console.log('[fetchFilterConfig] API返回完整数据:', JSON.stringify(res, null, 2));
        if (res.code === 0) {
          const filterModules = res.data?.filterModules || [];
          console.log('[fetchFilterConfig] filterModules:', filterModules);
          const allFilters = [];
          filterModules.forEach(module => {
            if (module.filters && Array.isArray(module.filters)) {
              const moduleFilters = module.filters.map(filter => {
                // 场景筛选项不再强制required，按API配置
                // 特殊处理转人工和满意度筛选项，强制设置为多选
                const isMultiSelect = filter.type === 'multiSelect' ||
                                    filter.id === 'transferStaff' ||
                                    filter.id === 'stars';
                return {
                  key: filter.id,
                  label: filter.name,
                  type: getFilterType(filter.type),
                options: filter.options || [],
                  multiple: isMultiSelect,
                  required: !!filter.required,
                  hidden: false,
                  selected: !!filter.selected
                };
              });
              allFilters.push(...moduleFilters);
            }
          });
          // 手动添加业务、子业务、触发task等筛选项（如果API中没有返回）
          const requiredFilters = [
            { key: 'bu', label: '业务', type: 'select', required: false, hidden: false, selected: true },
            { key: 'subBu', label: '子业务', type: 'select', required: false, hidden: false, selected: true },
            { key: 'taskKey', label: '触发task', type: 'select', required: false, hidden: false, selected: true },
            { key: 'taskVersion', label: '触发task版本', type: 'select', required: false, hidden: false, selected: false },
            { key: 'taskNode', label: '触发task节点', type: 'select', required: false, hidden: false, selected: false }
          ];

          // 检查并添加缺失的筛选项
          requiredFilters.forEach(requiredFilter => {
            const exists = allFilters.some(filter => filter.key === requiredFilter.key);
            if (!exists) {
              console.log('[fetchFilterConfig] 添加缺失的筛选项:', requiredFilter.key);
              allFilters.push(requiredFilter);
            }
          });

          availableFilters.value = allFilters;
          console.log('[fetchFilterConfig] availableFilters:', availableFilters.value);
          // 临时清除localStorage来测试默认筛选项
          localStorage.removeItem(SELECTED_FILTERS_KEY);

          // 优先本地缓存，否则用API默认
          loadSelectedFiltersFromLocalStorage();
          if (!selectedFilters.value.length) {
            selectedFilters.value = allFilters.filter(f => f.required || f.selected);
            console.log('[fetchFilterConfig] 使用默认选中的筛选项:', selectedFilters.value.map(f => f.key));
          } else {
            console.log('[fetchFilterConfig] 使用本地缓存的筛选项:', selectedFilters.value.map(f => f.key));
          }
          console.log('[fetchFilterConfig] 最终selectedFilters:', selectedFilters.value);

          // 检查业务筛选项是否在selectedFilters中
          const hasBuFilter = selectedFilters.value.some(f => f.key === 'bu');
          console.log('[fetchFilterConfig] 业务筛选项是否已选中:', hasBuFilter);
        } else {
          message.error(res.message || '获取筛选配置失败');
        }
      } catch (error) {
        message.error('获取筛选配置失败');
      }
    };

    // 根据API返回的filter.type转换为组件适用的type
    const getFilterType = (apiType) => {
      // 根据原始应用的类型映射
      switch (apiType) {
        case 'input':
          return 'input';
        case 'select':
          return 'select';
        case 'date':
          return 'date';
        case 'multiSelect':
          return 'select'; // 多选select
        default:
          return 'select';
      }
    };

    // 处理场景关系配置
    const fetchSceneRelationConfig = async () => {
      try {
        const res = await getSessionConditionScene();
        console.log('[fetchSceneRelationConfig] API返回:', res);
        console.log('[fetchSceneRelationConfig] API返回完整数据:', JSON.stringify(res, null, 2));
        if (res.code === 0 && res.data) {
          const sceneInfos = res.data.sceneInfos || [];
          console.log('[fetchSceneRelationConfig] sceneInfos:', sceneInfos);
          console.log('[fetchSceneRelationConfig] sceneInfos详细:', JSON.stringify(sceneInfos, null, 2));

          // 直接使用API返回的数据结构，与online-search组件兼容
          sceneRelationConfig.value = {
            sceneInfos: sceneInfos
          };

          // 添加额外的解析字段用于兼容旧代码（如果需要）
          sceneRelationConfig.value.sceneOptions = sceneInfos.map(scene => ({
            value: scene.sceneId,
            label: scene.sceneName,
            raw: scene
          }));

          // 日志输出关键信息
          console.log('[fetchSceneRelationConfig] 最终配置:', sceneRelationConfig.value);
          console.log('[fetchSceneRelationConfig] sceneInfos数量:', sceneInfos.length);

          // 检查每个场景的业务信息
          sceneInfos.forEach((scene, index) => {
            console.log(`[fetchSceneRelationConfig] 场景${index + 1}:`, {
              sceneId: scene.sceneId,
              sceneName: scene.sceneName,
              buInfosCount: scene.buInfos ? scene.buInfos.length : 0,
              buInfos: scene.buInfos
            });
          });

        } else {
          sceneRelationConfig.value = {
            sceneInfos: []
          };
          message.error(res.message || '获取场景关系配置失败');
        }
      } catch (error) {
        console.error('[fetchSceneRelationConfig] 错误:', error);
        sceneRelationConfig.value = {
          sceneInfos: []
        };
        message.error('获取场景关系配置失败');
      }
    };

    // 获取表格数据
    const fetchData = async (page = pagination.current, pageSize = pagination.pageSize) => {
      loading.value = true;
      try {
        // 构建查询参数
        const params = {
          ...buildQueryParams()
        };

        // pageNum和pageSize已经在buildQueryParams中设置了
        // 如果直接传入了page和pageSize，则覆盖buildQueryParams中的值
        if (page !== pagination.current) {
          params.pageNum = page;
        }

        if (pageSize !== pagination.pageSize) {
          params.pageSize = pageSize;
        }

        console.log('请求参数:', params); // 添加日志输出，方便调试
        const res = await getSessionPage(params);
        console.log('API返回数据:', res); // 添加日志输出，方便调试

        // 检查API返回状态
        if (res.code === 0) { // 原始应用API成功状态码为0
          // 适配原始应用的返回格式
          // 原始应用返回格式: { code: 0, data: { data: [...], totalNum: number, cost: number } }
          const responseData = res.data?.data || [];

          // 处理数据，确保字段正确映射
          tableData.value = responseData.map(item => ({
            ...item,
            // 确保这些字段存在，如果不存在则使用默认值
            sessionSolved: item.sessionSolved || null,
            stars: convertStarsToNumber(item.stars),
            transferStaff: item.transferStaff || null
          }));

          console.log('处理后的表格数据:', tableData.value); // 添加日志输出，方便调试
          pagination.total = res.data?.totalNum || 0;
        } else {
          message.error(res.message || '获取数据失败');
        }
      } catch (error) {
        console.error('获取数据失败', error);
        message.error('获取数据失败');
      } finally {
        loading.value = false;
      }
    };

    // 构建查询参数
    const buildQueryParams = () => {
      // 处理会话ID参数 - 支持多个ID，以逗号分隔
      let processedSessionId = '';
      if (searchFormData.sessionId && searchFormData.sessionId.trim()) {
        // 去除空格并按逗号分割，过滤空值
        const sessionIds = searchFormData.sessionId
          .split(',')
          .map(id => id.trim())
          .filter(id => id.length > 0);

        if (sessionIds.length > 0) {
          processedSessionId = sessionIds.join(',');
        }
      }

      // 创建基础参数
      const params = {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
        channel: 'ONLINE', // 在线会话类型
        sessionId: processedSessionId,
        visitId: searchFormData.visitId || '',
        applicationVersionId: searchFormData.applicationVersionId || '',
        scene: searchFormData.scene || '',
        bu: searchFormData.bu || '',
        subBu: searchFormData.subBu || '',
        taskKey: searchFormData.taskKey || '',
        taskVersion: searchFormData.taskVersion || '',
        taskNode: searchFormData.taskNode || '',
        userInput: searchFormData.userInput || '',
        finalOutput: searchFormData.finalOutput || ''
      };

      // 处理日期参数 - 始终包含日期参数
      const date = searchFormData.date;
      if (date && date[0]) {
        params.startTime = date[0].valueOf(); // 转换为时间戳
      } else {
        // 如果没有选择日期，使用默认时间范围（最近7天）
        params.startTime = dayjs().subtract(7, 'day').startOf('day').valueOf();
      }

      if (date && date[1]) {
        params.endTime = date[1].valueOf(); // 转换为时间戳
      } else {
        // 如果没有选择日期，使用当前时间作为结束时间
        params.endTime = dayjs().endOf('day').valueOf();
      }

      // 处理工作空间和应用参数
      const workspaceAndApp = searchFormData.workspaceAndApp;
      if (workspaceAndApp && workspaceAndApp[0]) params.workspaceId = workspaceAndApp[0];
      if (workspaceAndApp && workspaceAndApp[1]) params.applicationId = workspaceAndApp[1];

      // 处理其他动态筛选参数
      Object.keys(searchFormData).forEach(key => {
        // 排除已经处理过的字段和空值
        if (!['date', 'workspaceAndApp', 'sessionId', 'visitId', 'applicationVersionId', 'scene', 'bu', 'subBu', 'taskKey', 'taskVersion', 'taskNode', 'userInput', 'finalOutput'].includes(key)
            && searchFormData[key] !== null
            && searchFormData[key] !== undefined
            && searchFormData[key] !== '') {
          params[key] = searchFormData[key];
        }
      });

      // 处理排序
      if (searchFormData.sortField && searchFormData.sortOrder) {
        params.sortField = searchFormData.sortField;
        params.sortOrder = searchFormData.sortOrder;
      }

      return params;
    };

    // 处理搜索
    const handleSearch = () => {
      pagination.current = 1;
      fetchData(1);
    };

    // 清除单个筛选项
    const clearFilter = (key) => {
      if (key === 'date') {
        // 重置日期为默认的最近7天
        searchFormData.date = [
          dayjs().subtract(7, 'day').startOf('day'),
          dayjs().endOf('day')
        ];
      } else if (key === 'sessionId' || key === 'visitId' || key === 'userInput' || key === 'finalOutput') {
        // 输入框类型重置为空字符串
        searchFormData[key] = '';
      } else if (key === 'workspaceAndApp' || key === 'applicationVersionId' || key === 'scene') {
        // 基础选择框字段重置为null
        searchFormData[key] = null;
      } else {
        // 动态添加的筛选条件，根据配置重置
        const filter = (availableFilters.value || []).find(f => f.key === key);
        if (filter) {
          if (filter.type === 'select' && filter.multiple) {
            searchFormData[key] = []; // 多选重置为空数组
          } else if (filter.type === 'select') {
            searchFormData[key] = null; // 单选重置为null
          } else if (filter.type === 'input') {
            searchFormData[key] = ''; // 输入框重置为空字符串
          } else {
            searchFormData[key] = null; // 其他类型重置为null
          }
        } else {
          // 特殊处理转人工和满意度筛选项（强制多选）
          if (key === 'transferStaff' || key === 'stars') {
            searchFormData[key] = []; // 多选重置为空数组
          } else {
            // 如果找不到对应的筛选配置，则重置为null
            searchFormData[key] = null;
          }
        }
      }
    };

    // 处理清空
    const handleClearClick = () => {
      // 重置表单数据，但保留筛选项的显示状态
      Object.keys(searchFormData).forEach(key => {
        if (key === 'date') {
          // 重置日期为默认的最近7天
          searchFormData.date = [
            dayjs().subtract(7, 'day').startOf('day'),
            dayjs().endOf('day')
          ];
        } else if (key === 'sessionId' || key === 'visitId' || key === 'userInput' || key === 'finalOutput') {
          // 输入框类型重置为空字符串
          searchFormData[key] = '';
        } else if (key === 'workspaceAndApp' || key === 'applicationVersionId' || key === 'scene') {
          // 基础选择框字段重置为null
          searchFormData[key] = null;
        } else {
          // 动态添加的筛选条件，根据配置重置
          const filter = (availableFilters.value || []).find(f => f.key === key);
          if (filter) {
            if (filter.type === 'select' && filter.multiple) {
              searchFormData[key] = []; // 多选重置为空数组
            } else if (filter.type === 'select') {
              searchFormData[key] = null; // 单选重置为null
            } else if (filter.type === 'input') {
              searchFormData[key] = ''; // 输入框重置为空字符串
            } else {
              searchFormData[key] = null; // 其他类型重置为null
            }
          } else {
            // 特殊处理转人工和满意度筛选项（强制多选）
            if (key === 'transferStaff' || key === 'stars') {
              searchFormData[key] = []; // 多选重置为空数组
            } else {
              // 如果找不到对应的筛选配置，则重置为null
              searchFormData[key] = null;
            }
          }
        }
      });

      // 注意：不清除 selectedFilters.value，保持筛选项的显示状态
      // 清除localStorage
      localStorage.removeItem(LOCAL_STORAGE_KEY);

      pagination.current = 1;
      // 重新加载数据
      fetchData(1);
    };

    // handleAddFilterClick弹窗选择后，更新selectedFilters并写入localStorage
    const handleAddFilterClick = () => {
      availableFilters.value = (availableFilters.value || []).map(filter => ({
        ...filter,
        selected: selectedFilters.value.some(selected => selected.key === filter.key)
      }));
      filterConditionModalVisible.value = true;
    };
    const handleFilterConfirm = (filters) => {
      selectedFilters.value = filters;
      filterConditionModalVisible.value = false;
    };

    // watch selectedFilters，自动同步到localStorage
    watch(selectedFilters, (filters) => {
      const keys = filters.map(f => f.key);
      localStorage.setItem(SELECTED_FILTERS_KEY, JSON.stringify(keys));
    }, { deep: true });

    // 判断筛选项是否添加
    const isFilterAdded = (key) => {
      return selectedFilters.value.some(filter => filter.key === key);
    };

    // 判断筛选项是否有值（支持数组）
    const hasFilterValue = (key) => {
      const value = searchFormData[key];
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== undefined && value !== null && value !== '';
    };

    // 监听选中的筛选条件变化，动态显示或隐藏筛选项
    watch(() => selectedFilters.value, (newFilters) => {
      // 当选中的筛选条件变化时，检查是否有新添加的筛选条件需要初始化表单数据
      newFilters.forEach(filter => {
        if (searchFormData[filter.key] === undefined) {
          // 特殊处理转人工和满意度筛选项（强制多选）
          if (filter.key === 'transferStaff' || filter.key === 'stars') {
            searchFormData[filter.key] = []; // 多选默认为空数组
          } else if (filter.type === 'select' && filter.multiple) {
            searchFormData[filter.key] = []; // 多选默认为空数组
          } else if (filter.type === 'select') {
            searchFormData[filter.key] = null; // 单选默认为null
          } else if (filter.type === 'date') {
            searchFormData[filter.key] = null; // 日期默认为null
          } else {
            searchFormData[filter.key] = ''; // 输入框默认为空字符串
          }
        }
      });
    }, {deep: true});

    // 处理表格变更
    const handleTableChange = (pag, filters, sorter) => {
      // 处理排序
      if (sorter) {
        const {field, order} = sorter;
        searchFormData.sortField = field;
        searchFormData.sortOrder = order === 'ascend' ? 'asc' : order === 'descend' ? 'desc' : undefined;
      } else {
        searchFormData.sortField = undefined;
        searchFormData.sortOrder = undefined;
      }

      // 处理分页
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;

      // 获取数据
      fetchData(pag.current, pag.pageSize);
    };

    // 处理选择变更
    const onSelectChange = (selectedRowKeys) => {
      selectedRowKeys.value = selectedRowKeys;
    };

    // 转换满意度值为数字
    const convertStarsToNumber = (stars) => {
      // 如果是null或undefined，直接返回null
      if (stars === null || stars === undefined) {
        return null;
      }

      // 如果已经是数字且在有效范围内，直接返回
      if (typeof stars === 'number' && !isNaN(stars) && stars >= 0 && stars <= 5) {
        return stars;
      }

      // 如果是字符串，尝试处理
      if (typeof stars === 'string') {
        // 去除前后空格
        const trimmedStars = stars.trim();

        // 如果是空字符串或特殊文本，返回null
        if (trimmedStars === '' || trimmedStars === '未评价' || trimmedStars === 'N/A' || trimmedStars === '-') {
          return null;
        }

        // 尝试转换为数字
        const numValue = parseFloat(trimmedStars);
        if (!isNaN(numValue) && numValue >= 0 && numValue <= 5) {
          return numValue;
        }
      }

      // 其他情况返回null
      return null;
    };

    // 获取解决标签颜色
    const getSolvedTagColor = (solved) => {
      if (solved === null || solved === undefined) return '';

      // 处理不同类型的解决状态
      if (typeof solved === 'boolean') {
        return solved ? 'success' : 'error';
      }

      if (typeof solved === 'string') {
        // 处理字符串类型
        const mapping = {
          'Y': 'success',
          'N': 'error',
          'U': '',
          'true': 'success',
          'false': 'error',
          '是': 'success',
          '否': 'error',
          '已解决': 'success',
          '未解决': 'error',
          '未评价': ''
        };

        return mapping[solved] || '';
      }

      // 默认情况
      return '';
    };

    // 获取解决标签文本
    const getSolvedTagText = (solved) => {
      if (solved === null || solved === undefined) return '-';

      // 处理不同类型的解决状态
      if (typeof solved === 'boolean') {
        return solved ? '已解决' : '未解决';
      }

      if (typeof solved === 'string') {
        // 处理字符串类型
        if (solved === 'Y' || solved === 'true' || solved === '是') {
          return '已解决';
        }

        if (solved === 'N' || solved === 'false' || solved === '否') {
          return '未解决';
        }

        if (solved === 'U') {
          return '未评价';
        }

        // 如果已经是完整的文本，直接返回
        if (solved === '已解决' || solved === '未解决' || solved === '未评价') {
          return solved;
        }

        // 如果是其他字符串，直接返回
        return solved;
      }

      // 默认情况
      return '-';
    };

    // 获取场景颜色
    const getScenarioColor = (sceneName) => {
      // 根据场景名称返回不同的颜色
      // 这里使用一些常见场景的映射，可以根据实际需求调整
      const colorMap = {
        '外客在线智能': 'blue',
        '骑手在线智能': 'green',
        '拼好饭在线智能': 'orange',
        '拼好饭在线虚拟客服': 'purple',
        '外商在线智能': 'cyan',
        '外客直出': 'magenta',
        '外客履约': 'blue',
        '外客售后': 'orange',
        '外客全场景': 'green'
      };

      // 如果没有匹配的场景，返回默认颜色
      return colorMap[sceneName] || 'blue';
    };

    // 获取转人工标签颜色
    const getTransferStaffTagColor = (transferStaff) => {
      if (transferStaff === null || transferStaff === undefined) return '';

      // 处理不同类型的转人工状态
      if (typeof transferStaff === 'boolean') {
        return transferStaff ? 'blue' : 'green';
      }

      if (typeof transferStaff === 'string') {
        // 处理字符串类型
        const mapping = {
          'Y': 'blue',
          'N': 'green',
          'true': 'blue',
          'false': 'green',
          '是': 'blue',
          '否': 'green',
          '已转人工': 'blue',
          '未转人工': 'green'
        };

        return mapping[transferStaff] || 'blue';
      }

      // 默认情况
      return 'blue';
    };

    // 获取转人工标签文本
    const getTransferStaffTagText = (transferStaff) => {
      if (transferStaff === null || transferStaff === undefined) return '-';

      // 处理不同类型的转人工状态
      if (typeof transferStaff === 'boolean') {
        return transferStaff ? '已转人工' : '未转人工';
      }

      if (typeof transferStaff === 'string') {
        // 处理字符串类型
        if (transferStaff === 'Y' || transferStaff === 'true' || transferStaff === '是') {
          return '已转人工';
        }

        if (transferStaff === 'N' || transferStaff === 'false' || transferStaff === '否') {
          return '未转人工';
        }

        // 如果已经是完整的文本，直接返回
        if (transferStaff === '已转人工' || transferStaff === '未转人工') {
          return transferStaff;
        }

        // 如果是其他字符串，直接返回
        return transferStaff;
      }

      // 默认情况
      return '已转人工';
    };

    // 查看会话详情
    const viewSessionDetail = (record) => {
      // router.push({
      //   name: 'SessionDetail',
      //   params: {sessionId: record.sessionId}
      // });

      // router.resolve({
      //   name: 'SessionDetail',
      //   params: {sessionId: record.sessionId}
      // }).href

      window.open(router.resolve({
        name: 'SessionDetail',
        params: {sessionId: record.sessionId}
      }).href, '_blank');

    };

    // 处理导出选中
    const handleExportSelectedClick = () => {
      if (!hasSelectedRows.value) {
        message.warning('请先选择要导出的记录');
        return;
      }

      // 导出选中记录的逻辑
      // TODO: 实现导出功能
      message.success(`成功导出 ${selectedRowKeys.value.length} 条记录`);
    };

    // 应用收藏的筛选条件
    const applyFavoriteFilter = (filterToApply) => { // Removed :any type annotation
      isApplyingSavedFilter.value = true;
      // Ensure filterToApply and its properties exist before trying to use them
      const formDataRecord = filterToApply && filterToApply.conditionRecords && Array.isArray(filterToApply.conditionRecords)
                             ? filterToApply.conditionRecords.find((cr) => cr && cr.conditionId === '_savedFormData')
                             : null;

      if (formDataRecord && formDataRecord.conditionValue) {
        const savedFormData = JSON.parse(JSON.stringify(formDataRecord.conditionValue));

        Object.keys(searchFormData).forEach(key => {
          if (key === 'date') searchFormData.date = null;
          else if (typeof searchFormData[key] === 'string') searchFormData[key] = '';
          else searchFormData[key] = undefined;
        });

        Object.keys(savedFormData).forEach(key => {
          if (key === 'date') {
            const dateVal = savedFormData.date;
            if (Array.isArray(dateVal) && dateVal.length === 2) {
                const startDate = dateVal[0] ? dayjs(dateVal[0]) : null;
                const endDate = dateVal[1] ? dayjs(dateVal[1]) : null;
                let parsedDate = null;
                if (startDate && endDate && startDate.isValid() && endDate.isValid()) parsedDate = [startDate, endDate];
                else if (startDate && startDate.isValid()) parsedDate = [startDate, null];
                else if (endDate && endDate.isValid()) parsedDate = [null, endDate];
                searchFormData.date = parsedDate;
            } else {
                searchFormData.date = null;
            }
          } else {
            searchFormData[key] = savedFormData[key];
          }
        });

        const availableFiltersValue = availableFilters.value || [];
        const dynamicKeys = Object.keys(savedFormData).filter(k =>
            !['sessionId', 'date', 'workspaceAndApp', 'applicationVersionId', 'scene', 'visitId', 'userInput', 'finalOutput'].includes(k) &&
            availableFiltersValue.some(af => af.key === k)
        );
        selectedFilters.value = dynamicKeys;

        message.success(`已应用常用筛选: ${filterToApply.recordName}`);
        handleSearch();
      } else {
        message.error('无法应用常用筛选：格式不正确或筛选条件为空');
      }
      isApplyingSavedFilter.value = false;
    };

    // 打开管理收藏模态框
    const openManageFavoritesModal = () => {
      // TODO: 实现管理收藏模态框
    };

    // 打开保存筛选条件模态框
    const openSaveFilterModal = () => {
      // 设置当前筛选条件
      currentFilter.conditions = {...searchFormData};
      saveFilterModalVisible.value = true;
    };

    // 处理保存筛选条件 - Now targets backend API
    const handleSaveFilter = async (nameFromModal) => {
      if (!nameFromModal.trim()) {
        message.warning('筛选名称不能为空');
        return;
      }
      if (nameFromModal.trim().length > 20) {
        message.warning('筛选名称不能超过20个字符');
        return;
      }

      const conditionsToSave = JSON.parse(JSON.stringify(searchFormData));
      const payload = {
        recordName: nameFromModal.trim(),
        conditionRecords: [
          {
            conditionId: '_savedFormData',
            conditionName: '完整表单数据',
            conditionValue: conditionsToSave,
            conditionDisplayValue: '完整表单数据',
          },
        ],
        analysisType: FAVORITE_FILTER_ANALYSIS_TYPE,
      };

      try {
        console.log('Attempting to save favorite filter...'); // Debug log
        const response = await saveFilterCondition(payload);
        if (response && response.code === 0) {
          message.success('常用筛选保存成功');
          await loadFavoriteFilters(); // Refresh the list of favorite filters
      saveFilterModalVisible.value = false;
        } else {
          message.error(response?.message || '保存常用筛选失败');
        }
      } catch (error) {
        console.error('保存常用筛选失败 API call error:', error);
        message.error('保存常用筛选失败，请稍后重试');
      }
    };

    // 日期禁用
    const disabledDate = (current) => {
      // 禁用未来日期
      return current && current.valueOf() > Date.now();
    };

    // 日期格式
    const dateFormat = 'YYYY-MM-DD HH:mm:ss';

    // 从 localStorage 加载筛选条件 (for searchFormData)
    const loadFiltersFromLocalStorage = () => {
      const savedFiltersRaw = localStorage.getItem(LOCAL_STORAGE_KEY);
      if (savedFiltersRaw) {
        try {
          const savedFilters = JSON.parse(savedFiltersRaw);
          // 特殊处理日期范围
          if (savedFilters.date && Array.isArray(savedFilters.date) && savedFilters.date.length === 2) {
            const startDate = savedFilters.date[0] ? dayjs(savedFilters.date[0]) : null;
            const endDate = savedFilters.date[1] ? dayjs(savedFilters.date[1]) : null;
            if (startDate && endDate && startDate.isValid() && endDate.isValid()) {
              searchFormData.date = [startDate, endDate];
            } else if (startDate && startDate.isValid()) {
              searchFormData.date = [startDate, null];
            } else if (endDate && endDate.isValid()) {
              searchFormData.date = [null, endDate];
            }
             else {
              searchFormData.date = null;
            }
          } else {
            searchFormData.date = null;
          }
          // 合并其他筛选条件
          Object.keys(savedFilters).forEach(key => {
            if (key !== 'date') {
              searchFormData[key] = savedFilters[key];
            }
          });

          const availableFiltersValue = availableFilters.value || [];
          const previouslySelectedDynamicKeys = Object.keys(savedFilters).filter(k =>
            !['sessionId', 'date', 'workspaceAndApp', 'applicationVersionId', 'scene', 'visitId', 'userInput', 'finalOutput'].includes(k) &&
            availableFiltersValue.some(af => af.key === k)
          );
          selectedFilters.value = previouslySelectedDynamicKeys;

        } catch (error) {
          console.error('Failed to parse filters from localStorage:', error);
          localStorage.removeItem(LOCAL_STORAGE_KEY);
        }
      }
    };

    // 保存筛选条件到 localStorage (for searchFormData)
    const saveFiltersToLocalStorage = (filters) => {
      try {
        const filtersToSave = JSON.parse(JSON.stringify(filters));
        if (filtersToSave.date && Array.isArray(filtersToSave.date)) {
          filtersToSave.date = [
            filtersToSave.date[0] ? dayjs(filtersToSave.date[0]).toISOString() : null,
            filtersToSave.date[1] ? dayjs(filtersToSave.date[1]).toISOString() : null,
          ];
        } else {
           filtersToSave.date = null;
        }
        localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(filtersToSave));
      } catch (error) {
        console.error('Failed to save searchFormData to localStorage:', error);
      }
    };

    // 从 API 加载常用筛选条件
    const loadFavoriteFilters = async () => {
      try {
        // console.log('Attempting to load favorite filters...'); // Debug log
        const response = await getSavedFilterList(FAVORITE_FILTER_ANALYSIS_TYPE);
        if (response && response.code === 0 && Array.isArray(response.data)) {
          favoriteFilters.value = response.data.map(fav => {
            // Assuming the API returns an array of objects,
            // each with at least 'id' (as recordId) and 'recordName'
            const mappedFav = {
              id: fav.recordId, // Changed from fav.id to fav.recordId
              name: fav.recordName,
              conditionRecords: fav.conditionRecords
            };

            // Date parsing logic for 'conditionValue.date' if it exists in conditionRecords
            const formDataRecord = fav.conditionRecords?.find(cr => cr.conditionId === '_savedFormData');
            if (formDataRecord && formDataRecord.conditionValue && formDataRecord.conditionValue.date) {
              const dateVal = formDataRecord.conditionValue.date;
              if (Array.isArray(dateVal) && dateVal.length === 2) {
                const startDate = dateVal[0] ? dayjs(dateVal[0]) : null;
                const endDate = dateVal[1] ? dayjs(dateVal[1]) : null;
                let parsedDate = null;
                if (startDate && endDate && startDate.isValid() && endDate.isValid()) parsedDate = [startDate, endDate];
                else if (startDate && startDate.isValid()) parsedDate = [startDate, null];
                else if (endDate && endDate.isValid()) parsedDate = [null, endDate];
                // Ensure the date is stored back into the structure expected by applyFavoriteFilter
                if (mappedFav.conditionRecords) {
                    const recordToUpdate = mappedFav.conditionRecords.find(cr => cr.conditionId === '_savedFormData');
                    if (recordToUpdate && recordToUpdate.conditionValue) {
                        recordToUpdate.conditionValue.date = parsedDate;
                    }
                }
              }
            }
            return mappedFav;
          });
        } else {
          favoriteFilters.value = [];
          if (response && response.code !== 0) message.error(response.message || '获取常用筛选列表失败');
          // else if (!response) console.error('getSavedFilterList did not return a response'); // Corrected API name
        }
      } catch (error) {
        console.error('获取常用筛选列表失败 API call error:', error);
        message.error('获取常用筛选列表失败，请稍后重试');
        favoriteFilters.value = [];
      }
    };

    // 删除常用筛选
    const handleDeleteFavoriteFilter = async (filterToDelete) => { // Changed parameter to filter object
      if (!filterToDelete || !filterToDelete.id) {
        message.error('无法删除筛选：缺少必要信息');
        return;
      }
      try {
        // console.log(`Attempting to delete favorite filter with ID: ${filterToDelete.id}`); // Debug log
        const response = await deleteSavedFilter(filterToDelete.id);
        if (response && response.code === 0) {
          message.success(`已移除常用筛选 "${filterToDelete.name}"`); // Made message more specific
          await loadFavoriteFilters(); // Refresh the list
        } else {
          message.error(response?.message || '删除常用筛选失败');
        }
      } catch (error) {
        console.error('删除常用筛选失败 API call error:', error);
        message.error('删除常用筛选失败，请稍后重试');
      }
    };

    // 打开会话ID批量输入模态框
    const openSessionIdModal = () => {
      // 将当前输入框的值复制到临时变量
      sessionIdTempValue.value = searchFormData.sessionId;
      sessionIdModalVisible.value = true;
    };

    // 处理会话ID模态框确认
    const handleSessionIdModalOk = () => {
      // 处理输入的会话ID
      const processedIds = processSessionIds(sessionIdTempValue.value);
      searchFormData.sessionId = processedIds;
      sessionIdModalVisible.value = false;
    };

    // 处理会话ID模态框取消
    const handleSessionIdModalCancel = () => {
      sessionIdModalVisible.value = false;
      // 不保存临时值的更改
    };

    // 处理会话ID输入，支持多种格式
    const processSessionIds = (input) => {
      if (!input || !input.trim()) {
        return '';
      }

      // 按行分割，然后按逗号分割，去除空值和重复
      const ids = input
        .split(/[\n,]/) // 按换行符或逗号分割
        .map(id => id.trim()) // 去除前后空格
        .filter(id => id.length > 0) // 过滤空值
        .filter((id, index, arr) => arr.indexOf(id) === index); // 去重

      return ids.join(',');
    };

    // 获取当前输入的会话ID数量
    const getSessionIdCount = () => {
      if (!sessionIdTempValue.value || !sessionIdTempValue.value.trim()) {
        return 0;
      }

      const ids = sessionIdTempValue.value
        .split(/[\n,]/)
        .map(id => id.trim())
        .filter(id => id.length > 0)
        .filter((id, index, arr) => arr.indexOf(id) === index);

      return ids.length;
    };

    // 保证scene筛选项始终可用
    watch(availableFilters, (filters) => {
      if (!filters.some(f => f.key === 'scene')) {
        availableFilters.value.unshift({ key: 'scene', label: '场景', type: 'select', required: true, hidden: false });
      }
    }, { immediate: true });

    // 自动初始化bu（可选功能，暂时注释掉）
    // watch(() => searchFormData.scene, (newSceneId) => {
    //   const scene = sceneRelationConfig.value.sceneInfos.find(s => s.sceneId === newSceneId);
    //   if (scene && scene.buInfos && scene.buInfos.length > 0) {
    //     searchFormData.bu = scene.buInfos[0].buId;
    //   } else {
    //     searchFormData.bu = null;
    //   }
    // });

    // 获取显示用的值
    const getDisplayValue = (key, value) => {
      if (Array.isArray(value)) {
        // 特殊处理转人工和满意度筛选项，显示有意义的标签
        if (key === 'transferStaff') {
          const transferStaffLabels = {
            'noChatRequest': '未转人工',
            'noChatBegin': '在线转人工（成功接入）',
            'chatBegin': '在线转人工（未接入）',
            'transferBD': '转BD'
          };
          return value.map(v => transferStaffLabels[v] || v).join(', ');
        } else if (key === 'stars') {
          return value.map(v => `${v}星`).join(', ');
        }
        return value.join(', ');
      }
      return value;
    };

    return {
      // Tab相关
      activeTab,

      // 会话查询相关
      loading,
      tableData,
      searchFormData,
      columns,
      pagination,
      selectedRowKeys,
      hasSelectedRows,
      filterConditionModalVisible,
      availableFilters,
      selectedFilters,
      visibleFilters,
      hasActiveFilters,
      sceneRelationConfig,
      favoriteFilters,
      currentFilter,
      saveFilterModalVisible,
      isApplyingSavedFilter,
      componentChangeHandlers,
      dateFormat,
      sessionIdModalVisible,
      sessionIdTempValue,
      openSessionIdModal,
      handleSessionIdModalOk,
      handleSessionIdModalCancel,
      getSessionIdCount,
      // 会话查询方法
      handleSearch,
      handleClearClick,
      clearFilter,
      handleAddFilterClick,
      handleFilterConfirm,
      isFilterAdded,
      hasFilterValue,
      getFilterOptions,
      handleWorkspaceAppChange,
      handleAppVersionChange,
      handleSceneChange,
      handleBusinessChange,
      handleSubBusinessChange,
      handleTableChange,
      onSelectChange,
      getSolvedTagColor,
      getSolvedTagText,
      getScenarioColor,
      getTransferStaffTagColor,
      getTransferStaffTagText,
      viewSessionDetail,
      handleExportSelectedClick,
      applyFavoriteFilter,
      openManageFavoritesModal,
      openSaveFilterModal,
      handleSaveFilter,
      disabledDate,
      loadFiltersFromLocalStorage,
      saveFiltersToLocalStorage,
      loadFavoriteFilters,
      handleDeleteFavoriteFilter,
      taskVersionLoading,
      taskNodeLoading,
      getDisplayValue,
    };
  }
});
</script>

<style scoped>
.session-analysis {
  padding: 20px;
}

.session-tabs {
  margin-bottom: 16px;
}

:deep(.session-tabs .ant-tabs-content-holder) {
  padding-top: 16px;
}

.filter-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.compact-form {
  width: 100%;
}

.compact-form-item {
  margin-bottom: 12px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
}

.table-actions {
  display: flex;
  align-items: center;
}

/* 已选择筛选项标签区域样式 */
.saved-filters-tags {
  display: flex;
  align-items: flex-start;
  margin-top: 16px;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.saved-filters-label {
  font-size: 14px;
  color: #666;
  margin-right: 12px;
  white-space: nowrap;
  line-height: 24px;
}

.saved-filters-list {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.saved-filter-tag {
  transition: all 0.3s;
}

.saved-filter-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.ant-table-column-title) {
  font-weight: 500;
}

.custom-button {
  border-radius: 8px;
  transition: all 0.3s;
  margin-right: 8px;
}

.custom-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>